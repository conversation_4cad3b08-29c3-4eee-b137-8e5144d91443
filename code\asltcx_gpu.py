import cupy as cp
from cupyx.scipy.signal import fftconvolve

# Dtype aliases for clarity in annotations
cp_float64 = cp.float64
cp_complex128 = cp.complex128


def asltcx(input_data, Fs, F, Ncyc, ord=None, mult=False):
    """
    GPU (CuPy) implementation of ASLTCX to output complex spectrum.

    Mirrors the CPU reference in [code/asltcx.py](code/asltcx.py:3) while adopting the
    implementation patterns from [code/fasltcx_gpu.py](code/fasltcx_gpu.py:9):
    - Uses CuPy arrays and fftconvolve on GPU
    - Stable multiplicative accumulation for magnitudes
    - Arithmetic averaging for unit-phase vectors
    - Supports both additive (default) and multiplicative (if mult=True) superresolution
    - Returns NumPy array for API compatibility with CPU code
    """
    # --- Input Validation (host) ---
    if F is None or len(F) == 0:
        raise ValueError("Frequencies not defined.")
    if input_data is None or input_data.size == 0:
        raise ValueError("Input data is empty.")

    # Ensure input_data is 2D (host side)
    if input_data.ndim == 1:
        # shape (1, N)
        input_data = input_data.reshape(1, -1)
    elif input_data.ndim > 2:
        raise ValueError("Input must be 1D or 2D.")

    Nbuffers, Npoints = input_data.shape
    nF = int(len(F))

    # --- Handle order parameter (host then to device) ---
    # CPU asltcx uses integer orders only (no fractional), matching length(F)
    if ord is not None and hasattr(ord, "__len__") and len(ord) == 2:
        # linearly spaced between ord[0] and ord[1], rounded to ints
        import numpy as np  # local import to avoid hard dependency at module import
        order_ls_host = np.round(np.linspace(float(ord[0]), float(ord[1]), nF)).astype(int)
        order_ls_host = np.maximum(order_ls_host, 1)  # ensure at least 1
    else:
        # Default order 1 for all frequencies (standard CWT)
        import numpy as np  # local import
        order_ls_host = np.ones(nF, dtype=int)

    # --- Move main arrays to GPU ---
    input_gpu = cp.asarray(input_data)
    F_gpu = cp.asarray(F, dtype=cp.float64)
    order_gpu = cp.asarray(order_ls_host, dtype=cp.int32)

    # --- Generate Wavelet Sets (GPU) ---
    wavelets = [[] for _ in range(nF)]
    padding = 0

    # Build wavelet bank exactly like CPU logic
    for i_freq in range(nF):
        Fc = float(F_gpu[i_freq].item())
        max_ord = int(order_gpu[i_freq].item())
        if mult:
            # Multiplicative superresolution:
            # Each new wavelet has Ncyc extra cycles (i_ord * Ncyc)
            # CPU version loops i_ord in [1..order]
            for i_ord in range(1, max_ord + 1):
                n_cyc = Ncyc * i_ord
                wl = cxmorlet(Fc, n_cyc, float(Fs))  # CuPy array complex128
                wavelets[i_freq].append(wl)
                half_len = wl.shape[0] // 2
                if half_len > padding:
                    padding = half_len
        else:
            # Additive superresolution:
            # Each additional order adds one cycle (Ncyc + i_ord), i_ord in [0..order-1]
            for i_ord in range(max_ord):
                n_cyc = Ncyc + i_ord
                wl = cxmorlet(Fc, n_cyc, float(Fs))  # CuPy array complex128
                wavelets[i_freq].append(wl)
                half_len = wl.shape[0] // 2
                if half_len > padding:
                    padding = half_len

    # --- Buffers (GPU) ---
    total_len = int(Npoints + 2 * padding)
    buffer: cp.ndarray = cp.zeros(total_len, dtype=cp_complex128)  # type: ignore[arg-type]
    wtresult: cp.ndarray = cp.zeros((nF, Npoints), dtype=cp_complex128)  # type: ignore[arg-type]

    bufbegin = int(padding)
    bufend = int(padding + Npoints)

    # --- Main Computation (GPU) ---
    for i_buf in range(Nbuffers):
        # Place current input slice into central part of buffer as complex
        buffer[bufbegin:bufend] = input_gpu[i_buf, :].astype(cp.complex128, copy=False)

        for i_freq in range(nF):
            # Init magnitude and phase accumulators
            mag_buffer: cp.ndarray = cp.ones(Npoints, dtype=cp_float64)  # type: ignore[arg-type]
            phase_buffer: cp.ndarray = cp.zeros(Npoints, dtype=cp_complex128)  # type: ignore[arg-type]

            max_ord = int(order_gpu[i_freq].item())
            # Convolve with each wavelet; restricted convolution
            for i_ord in range(max_ord):
                wl = wavelets[i_freq][i_ord]
                conv_full = fftconvolve(buffer, wl, mode="same")
                conv = conv_full[bufbegin:bufend]

                abs_conv = cp.abs(conv)
                # Avoid zeros in multiplicative magnitude accumulation
                mag_buffer *= cp.maximum(abs_conv, 1e-300)
                # Accumulate unit-phase vectors; guard division
                phase_buffer += conv / (abs_conv + 1e-12)

            # Geometric mean for magnitude, arithmetic mean for unit-phase sum
            root = 1.0 / float(max_ord if max_ord > 0 else 1)
            magnitude = cp.power(mag_buffer, root)
            phase = phase_buffer * root

            # Combine and accumulate
            wtresult[i_freq, :] += magnitude * phase

    # Average across buffers
    wtresult /= float(Nbuffers)

    # Return to NumPy for API consistency
    return cp.asnumpy(wtresult)


def cxmorlet(Fc, Nc, Fs):
    """
    Computes the complex Morlet wavelet (CuPy vectorized).
    Fc: center frequency (Hz)
    Nc: number of cycles
    Fs: sampling frequency (Hz)
    Returns: CuPy array complex128
    """
    # Avoid zero frequency by setting minimum frequency to 0.001 (preserve sign)
    if abs(Fc) < 1e-10:
        Fc = 0.001 if Fc >= 0 else -0.001

    Fc_cp = cp.asarray(Fc, dtype=cp.float64)
    Nc_cp = cp.asarray(Nc, dtype=cp.float64)
    Fs_cp = cp.asarray(Fs, dtype=cp.float64)

    # The last peak should be at 2.5 standard deviations
    sd = (Nc_cp / 2.0) * (1.0 / cp.abs(Fc_cp)) / 2.5

    # Window length as odd integer similar to CPU: wl = 2*floor(int(6*sd*Fs)/2)+1
    wl_float = 6.0 * sd * Fs_cp
    wl_even = int(2 * cp.floor(cp.fix(wl_float).astype(cp.float64) / 2.0).item())
    wl = wl_even + 1  # make it odd

    off = wl // 2
    i = cp.arange(wl, dtype=cp.float64)
    t = (i - off) / Fs_cp

    w = bw_cf(t, sd, Fc_cp)
    gi = gauss(t, sd).sum()
    w = w / gi
    # ensure dtype complex128
    return w.astype(cp.complex128, copy=False)


def bw_cf(t, bw, cf):
    """
    Computes complex wavelet coefficients (CuPy vectorized).
    """
    cnorm = 1.0 / (bw * cp.sqrt(2.0 * cp.pi))
    exp1 = cnorm * cp.exp(-(t ** 2) / (2.0 * bw ** 2))
    return cp.exp(2j * cp.pi * cf * t) * exp1


def gauss(t, sd):
    """
    Computes the Gaussian coefficient (CuPy vectorized).
    """
    cnorm = 1.0 / (sd * cp.sqrt(2.0 * cp.pi))
    return cnorm * cp.exp(-(t ** 2) / (2.0 * sd ** 2))