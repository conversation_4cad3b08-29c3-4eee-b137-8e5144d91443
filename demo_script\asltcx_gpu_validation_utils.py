#!/usr/bin/env python3
"""
GPU Validation Utilities for ASLTCX Implementation

This module provides specialized validation functions for comparing GPU and CPU
implementations of ASLTCX with detailed numerical analysis and error reporting.
"""

import numpy as np
import sys
import os
from typing import Dict, List, Tuple, Optional, Any
import warnings

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)


class ValidationResult:
    """Container for validation results with detailed analysis."""
    
    def __init__(self):
        self.passed = False
        self.shape_match = False
        self.dtype_match = False
        self.max_abs_error = 0.0
        self.mean_abs_error = 0.0
        self.max_rel_error = 0.0
        self.mean_rel_error = 0.0
        self.correlation = 0.0
        self.snr_db = 0.0
        self.tolerance_used = 0.0
        self.details = []
        self.error_distribution = {}
        
    def add_detail(self, message: str):
        """Add a detail message."""
        self.details.append(message)
        
    def __str__(self):
        """String representation of validation results."""
        status = "PASSED" if self.passed else "FAILED"
        result = f"Validation {status}\n"
        result += f"  Shape match: {self.shape_match}\n"
        result += f"  Max absolute error: {self.max_abs_error:.2e}\n"
        result += f"  Mean absolute error: {self.mean_abs_error:.2e}\n"
        result += f"  Max relative error: {self.max_rel_error:.2e}\n"
        result += f"  Correlation: {self.correlation:.6f}\n"
        result += f"  SNR: {self.snr_db:.2f} dB\n"
        
        if self.details:
            result += "  Details:\n"
            for detail in self.details:
                result += f"    {detail}\n"
                
        return result


def advanced_numerical_comparison(cpu_result: np.ndarray, gpu_result: np.ndarray,
                                tolerance: float = 1e-10, 
                                relative_tolerance: float = 1e-8) -> ValidationResult:
    """
    Perform advanced numerical comparison between CPU and GPU results.
    
    Args:
        cpu_result: Result from CPU implementation
        gpu_result: Result from GPU implementation
        tolerance: Absolute tolerance for comparison
        relative_tolerance: Relative tolerance for comparison
    
    Returns:
        ValidationResult object with detailed analysis
    """
    result = ValidationResult()
    result.tolerance_used = tolerance
    
    # Basic shape and type checks
    result.shape_match = cpu_result.shape == gpu_result.shape
    result.dtype_match = cpu_result.dtype == gpu_result.dtype
    
    if not result.shape_match:
        result.add_detail(f"Shape mismatch: CPU {cpu_result.shape} vs GPU {gpu_result.shape}")
        return result
    
    if not result.dtype_match:
        result.add_detail(f"Dtype mismatch: CPU {cpu_result.dtype} vs GPU {gpu_result.dtype}")
    
    # Convert to same dtype for comparison if needed
    if cpu_result.dtype != gpu_result.dtype:
        if np.iscomplexobj(cpu_result) or np.iscomplexobj(gpu_result):
            cpu_cmp = cpu_result.astype(np.complex128)
            gpu_cmp = gpu_result.astype(np.complex128)
        else:
            cpu_cmp = cpu_result.astype(np.float64)
            gpu_cmp = gpu_result.astype(np.float64)
    else:
        cpu_cmp = cpu_result
        gpu_cmp = gpu_result
    
    # Calculate absolute errors
    abs_diff = np.abs(cpu_cmp - gpu_cmp)
    result.max_abs_error = np.max(abs_diff)
    result.mean_abs_error = np.mean(abs_diff)
    
    # Calculate relative errors
    cpu_magnitude = np.abs(cpu_cmp)
    nonzero_mask = cpu_magnitude > 1e-15
    
    if np.any(nonzero_mask):
        rel_errors = abs_diff[nonzero_mask] / cpu_magnitude[nonzero_mask]
        result.max_rel_error = np.max(rel_errors)
        result.mean_rel_error = np.mean(rel_errors)
    else:
        result.add_detail("Warning: All CPU values are effectively zero")
    
    # Calculate correlation coefficient
    if cpu_cmp.size > 1:
        cpu_flat = cpu_cmp.flatten()
        gpu_flat = gpu_cmp.flatten()
        
        if np.iscomplexobj(cpu_flat):
            # For complex data, calculate correlation of magnitudes
            cpu_mag = np.abs(cpu_flat)
            gpu_mag = np.abs(gpu_flat)
            if np.std(cpu_mag) > 1e-15 and np.std(gpu_mag) > 1e-15:
                result.correlation = np.corrcoef(cpu_mag, gpu_mag)[0, 1]
        else:
            if np.std(cpu_flat) > 1e-15 and np.std(gpu_flat) > 1e-15:
                result.correlation = np.corrcoef(cpu_flat, gpu_flat)[0, 1]
    
    # Calculate Signal-to-Noise Ratio
    signal_power = np.mean(np.abs(cpu_cmp)**2)
    noise_power = np.mean(abs_diff**2)
    if noise_power > 1e-30:
        result.snr_db = 10 * np.log10(signal_power / noise_power)
    else:
        result.snr_db = float('inf')
    
    # Error distribution analysis
    if abs_diff.size > 0:
        percentiles = [50, 90, 95, 99, 99.9]
        result.error_distribution = {
            f'p{p}': np.percentile(abs_diff, p) for p in percentiles
        }
    
    # Determine if validation passes
    abs_check = result.max_abs_error < tolerance
    rel_check = result.max_rel_error < relative_tolerance
    corr_check = result.correlation > 0.999999 if not np.isnan(result.correlation) else True
    
    result.passed = abs_check and rel_check and corr_check
    
    # Add detailed analysis
    if result.passed:
        result.add_detail("✓ Numerical comparison PASSED")
    else:
        result.add_detail("✗ Numerical comparison FAILED")
        if not abs_check:
            result.add_detail(f"  Absolute error too large: {result.max_abs_error:.2e} > {tolerance:.2e}")
        if not rel_check:
            result.add_detail(f"  Relative error too large: {result.max_rel_error:.2e} > {relative_tolerance:.2e}")
        if not corr_check:
            result.add_detail(f"  Correlation too low: {result.correlation:.6f} < 0.999999")
    
    result.add_detail(f"SNR: {result.snr_db:.2f} dB")
    result.add_detail(f"Error percentiles: 50%={result.error_distribution.get('p50', 0):.2e}, "
                     f"99%={result.error_distribution.get('p99', 0):.2e}")
    
    return result


def validate_complex_spectrum_properties(spectrum: np.ndarray, 
                                       freqs: np.ndarray,
                                       fs: float) -> Dict[str, Any]:
    """
    Validate properties of complex spectrum output.
    
    Args:
        spectrum: Complex spectrum array [n_freqs, n_time]
        freqs: Frequency array
        fs: Sampling frequency
    
    Returns:
        Dictionary with validation results
    """
    validation = {
        'is_complex': np.iscomplexobj(spectrum),
        'shape_valid': len(spectrum.shape) == 2,
        'freq_dimension_match': spectrum.shape[0] == len(freqs),
        'no_nan_values': not np.any(np.isnan(spectrum)),
        'no_inf_values': not np.any(np.isinf(spectrum)),
        'magnitude_range': (0, 0),
        'phase_range': (0, 0),
        'energy_conservation': True,
        'details': []
    }
    
    if not validation['is_complex']:
        validation['details'].append("✗ Spectrum is not complex-valued")
        return validation
    
    if not validation['shape_valid']:
        validation['details'].append(f"✗ Invalid shape: {spectrum.shape} (expected 2D)")
        return validation
    
    if not validation['freq_dimension_match']:
        validation['details'].append(f"✗ Frequency dimension mismatch: {spectrum.shape[0]} vs {len(freqs)}")
    
    # Calculate magnitude and phase
    magnitude = np.abs(spectrum)
    phase = np.angle(spectrum)
    
    validation['magnitude_range'] = (np.min(magnitude), np.max(magnitude))
    validation['phase_range'] = (np.min(phase), np.max(phase))
    
    # Check for NaN/Inf values
    if validation['no_nan_values']:
        validation['details'].append("✓ No NaN values found")
    else:
        nan_count = np.sum(np.isnan(spectrum))
        validation['details'].append(f"✗ Found {nan_count} NaN values")
    
    if validation['no_inf_values']:
        validation['details'].append("✓ No infinite values found")
    else:
        inf_count = np.sum(np.isinf(spectrum))
        validation['details'].append(f"✗ Found {inf_count} infinite values")
    
    # Magnitude range check
    mag_min, mag_max = validation['magnitude_range']
    if mag_min >= 0:
        validation['details'].append("✓ Magnitude values are non-negative")
    else:
        validation['details'].append(f"✗ Negative magnitude values found (min: {mag_min})")
    
    # Phase range check
    phase_min, phase_max = validation['phase_range']
    if -np.pi <= phase_min and phase_max <= np.pi:
        validation['details'].append("✓ Phase values in valid range [-π, π]")
    else:
        validation['details'].append(f"✗ Phase values outside [-π, π]: [{phase_min:.3f}, {phase_max:.3f}]")
    
    # Energy conservation check (simplified)
    total_energy = np.sum(magnitude**2)
    if total_energy > 0:
        validation['details'].append(f"Total energy: {total_energy:.2e}")
    else:
        validation['details'].append("✗ Zero total energy detected")
        validation['energy_conservation'] = False
    
    return validation


def run_parameter_sensitivity_test(test_function, base_params: Dict[str, Any],
                                  param_variations: Dict[str, List[Any]]) -> Dict[str, Any]:
    """
    Run parameter sensitivity test for GPU vs CPU comparison.
    
    Args:
        test_function: Function to test (should return CPU and GPU results)
        base_params: Base parameters for the test
        param_variations: Dictionary of parameter variations to test
    
    Returns:
        Dictionary with sensitivity test results
    """
    results = {
        'base_params': base_params,
        'variations': {},
        'summary': {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'max_error_seen': 0.0
        }
    }
    
    for param_name, param_values in param_variations.items():
        results['variations'][param_name] = {}
        
        for param_value in param_values:
            # Create test parameters
            test_params = base_params.copy()
            test_params[param_name] = param_value
            
            try:
                # Run test
                cpu_result, gpu_result = test_function(**test_params)
                
                # Validate results
                validation = advanced_numerical_comparison(cpu_result, gpu_result)
                
                results['variations'][param_name][str(param_value)] = {
                    'validation': validation,
                    'passed': validation.passed,
                    'max_error': validation.max_abs_error
                }
                
                # Update summary
                results['summary']['total_tests'] += 1
                if validation.passed:
                    results['summary']['passed_tests'] += 1
                else:
                    results['summary']['failed_tests'] += 1
                
                results['summary']['max_error_seen'] = max(
                    results['summary']['max_error_seen'],
                    validation.max_abs_error
                )
                
            except Exception as e:
                results['variations'][param_name][str(param_value)] = {
                    'error': str(e),
                    'passed': False
                }
                results['summary']['total_tests'] += 1
                results['summary']['failed_tests'] += 1
    
    return results


def print_validation_summary(validation_results: List[ValidationResult]):
    """Print summary of multiple validation results."""
    print("\n" + "="*50)
    print("VALIDATION SUMMARY")
    print("="*50)
    
    total_tests = len(validation_results)
    passed_tests = sum(1 for r in validation_results if r.passed)
    failed_tests = total_tests - passed_tests
    
    print(f"Total tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success rate: {100*passed_tests/total_tests:.1f}%")
    
    if validation_results:
        max_abs_errors = [r.max_abs_error for r in validation_results]
        max_rel_errors = [r.max_rel_error for r in validation_results]
        correlations = [r.correlation for r in validation_results if not np.isnan(r.correlation)]
        
        print(f"\nError Statistics:")
        print(f"  Max absolute error: {np.max(max_abs_errors):.2e}")
        print(f"  Mean absolute error: {np.mean(max_abs_errors):.2e}")
        print(f"  Max relative error: {np.max(max_rel_errors):.2e}")
        print(f"  Mean relative error: {np.mean(max_rel_errors):.2e}")
        
        if correlations:
            print(f"  Min correlation: {np.min(correlations):.6f}")
            print(f"  Mean correlation: {np.mean(correlations):.6f}")
    
    print("="*50)
