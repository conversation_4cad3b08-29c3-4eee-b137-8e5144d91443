import cupy as cp
from cupyx.scipy.signal import fftconvolve
from typing import Any

# Dtype aliases (help <PERSON><PERSON><PERSON> with type checking)
CP_FLOAT64: Any = cp.float64  # type: ignore[assignment]
CP_COMPLEX128: Any = cp.complex128  # type: ignore[assignment]

def gauss(t, sd):
    """
    Computes the Gaussian coefficient (CuPy vectorized).
    """
    cnorm = 1.0 / (sd * cp.sqrt(2.0 * cp.pi))
    return cnorm * cp.exp(-(t**2) / (2.0 * sd**2))

def bw_cf(t, bw, cf):
    """
    Computes complex wavelet coefficients (CuPy vectorized).
    """
    cnorm = 1.0 / (bw * cp.sqrt(2.0 * cp.pi))
    exp1 = cnorm * cp.exp(-(t**2) / (2.0 * bw**2))
    return cp.exp(2j * cp.pi * cf * t) * exp1

def cxmorlet(Fc, Nc, Fs):
    """
    Computes the complex Morlet wavelet (CuPy).
    """
    # Avoid zero frequency by setting minimum frequency to 0.001 maintaining sign
    if abs(Fc) < 1e-10:
        Fc = 0.001 if Fc >= 0 else -0.001

    Fc_cp = cp.asarray(Fc, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    Nc_cp = cp.asarray(Nc, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    Fs_cp = cp.asarray(Fs, dtype=CP_FLOAT64)  # type: ignore[arg-type]

    # The last peak should be at 2.5 standard deviations
    sd = (Nc_cp / 2.0) * (1.0 / cp.abs(Fc_cp)) / 2.5

    # window length ~ 6 * sd * Fs, force odd
    wl_float = 6.0 * sd * Fs_cp
    wl_even = int(2 * cp.floor(cp.fix(wl_float).astype(CP_FLOAT64) / 2.0).item())  # type: ignore[arg-type]
    wl = wl_even + 1

    off = wl // 2
    i = cp.arange(wl, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    t = (i - off) / Fs_cp

    w = bw_cf(t, sd, Fc_cp)
    gi = gauss(t, sd).sum()
    w = w / gi
    return w.astype(CP_COMPLEX128, copy=False)  # type: ignore[arg-type]

def aslt(input_data, Fs, F, Ncyc, ord=None, mult=False):
    """
    GPU-accelerated ASLT producing a real-valued superlet spectrum.

    Args:
        input_data (np.ndarray-like): Input data matrix of shape [buffers x samples] or 1D array.
        Fs (float): Sampling frequency in Hz.
        F (array-like): Array of frequencies of interest.
        Ncyc (float): Number of initial wavelet cycles.
        ord (list or tuple, optional): [min_order, max_order] integer range across frequencies.
                                       Defaults to None, which results in a standard CWT (order=1).
        mult (bool, optional): Multiplicative (True) or additive (False) superresolution.
    Returns:
        numpy.ndarray: The [frequencies x samples] superlet spectrum (host array).
    """
    # Basic input checks
    if F is None or len(F) == 0:
        raise ValueError("Frequencies not defined.")
    if input_data is None or getattr(input_data, "size", 0) == 0:
        raise ValueError("Input data is empty.")

    # Normalize shape on host, then move to GPU
    import numpy as _np
    if getattr(input_data, "ndim", 1) == 1:
        input_data = _np.reshape(input_data, (1, -1))

    Nbuffers, Npoints = input_data.shape

    # Determine integer orders per-frequency
    if ord is not None and len(ord) == 2:
        order_ls_host = _np.round(_np.linspace(ord[0], ord[1], len(F))).astype(int)
    else:
        order_ls_host = _np.ones(len(F), dtype=int)

    order_ls = cp.asarray(order_ls_host, dtype=cp.int32)
    F_gpu = cp.asarray(F, dtype=CP_FLOAT64)  # type: ignore[arg-type]

    # Build wavelet sets
    wavelets = [[] for _ in range(len(F))]
    padding = 0
    for i_freq in range(len(F)):
        center = float(F_gpu[i_freq].item())
        n_order = int(order_ls[i_freq].item())
        if mult:
            # multiplicative: cycles = Ncyc * i_ord (i_ord starts at 1)
            for i_ord in range(1, n_order + 1):
                w = cxmorlet(center, Ncyc * i_ord, float(Fs))
                wavelets[i_freq].append(w)
                padding = max(padding, int(len(w) // 2))
        else:
            # additive: cycles = Ncyc + i_ord (i_ord starts at 0)
            for i_ord in range(n_order):
                w = cxmorlet(center, Ncyc + i_ord, float(Fs))
                wavelets[i_freq].append(w)
                padding = max(padding, int(len(w) // 2))

    total_len = int(Npoints + 2 * padding)
    buffer = cp.zeros(total_len, dtype=CP_COMPLEX128)  # type: ignore[arg-type]
    wtresult = cp.zeros((len(F), Npoints), dtype=CP_FLOAT64)  # type: ignore[arg-type]

    bufbegin = int(padding)
    bufend = int(padding + Npoints)

    # Upload input once
    input_gpu = cp.asarray(input_data, dtype=CP_FLOAT64)  # type: ignore[arg-type]

    for i_buf in range(Nbuffers):
        # Place current buffer
        buffer[bufbegin:bufend] = input_gpu[i_buf, :].astype(CP_COMPLEX128, copy=False)  # type: ignore[arg-type]

        for i_freq in range(len(F)):
            # geometric mean pooling buffer
            temp = cp.ones(Npoints, dtype=CP_FLOAT64)  # type: ignore[arg-type]

            # Convolve with each wavelet in the current set
            for i_ord in range(len(wavelets[i_freq])):
                conv_full = fftconvolve(buffer, wavelets[i_freq][i_ord], mode="same")
                band = conv_full[bufbegin:bufend]
                # Accumulate squared magnitude times 2 to get full spectral energy
                temp *= (2.0 * cp.abs(band) ** 2)

            # Power for geometric mean
            root = 1.0 / float(order_ls[i_freq].item())
            temp = cp.power(temp, root)

            wtresult[i_freq, :] += temp

    wtresult /= float(Nbuffers)
    return cp.asnumpy(wtresult)