import cupy as cp
from cupyx.scipy.signal import fftconvolve
from typing import Any

# Wrap dtype constants through type: ignore casts for Pylance
CP_FLOAT64: Any = cp.float64  # type: ignore[assignment]
CP_COMPLEX128: Any = cp.complex128  # type: ignore[assignment]

def gauss(t, sd):
    """
    Computes the Gaussian coefficient (CuPy vectorized).
    """
    cnorm = 1.0 / (sd * cp.sqrt(2.0 * cp.pi))
    return cnorm * cp.exp(-(t**2) / (2.0 * sd**2))

def bw_cf(t, bw, cf):
    """
    Computes complex wavelet coefficients (CuPy vectorized).
    """
    cnorm = 1.0 / (bw * cp.sqrt(2.0 * cp.pi))
    exp1 = cnorm * cp.exp(-(t**2) / (2.0 * bw**2))
    return cp.exp(2j * cp.pi * cf * t) * exp1

def cxmorlet(Fc, Nc, Fs):
    """
    Computes the complex Morlet wavelet for the desired center frequency Fc with Nc cycles, with a sampling frequency Fs.
    Returns a CuPy array (complex128).
    """
    # Avoid zero frequency by setting minimum frequency to 0.001 maintaining sign
    if abs(Fc) < 1e-10:
        Fc = 0.001 if Fc >= 0 else -0.001

    Fc_cp = cp.asarray(Fc, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    Nc_cp = cp.asarray(Nc, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    Fs_cp = cp.asarray(Fs, dtype=CP_FLOAT64)  # type: ignore[arg-type]

    sd = (Nc_cp / 2.0) * (1.0 / cp.abs(Fc_cp)) / 2.5
    # window length ~ 6 * sd * Fs, force odd
    wl_float = 6.0 * sd * Fs_cp
    wl_even = int(2 * cp.floor(cp.fix(wl_float).astype(CP_FLOAT64) / 2.0).item())  # type: ignore[arg-type]
    wl = wl_even + 1

    off = wl // 2
    i = cp.arange(wl, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    t = (i - off) / Fs_cp

    w = bw_cf(t, sd, Fc_cp)
    gi = gauss(t, sd).sum()
    w = w / gi
    return w.astype(CP_COMPLEX128, copy=False)  # type: ignore[arg-type]

def is_fractional(x):
    """
    Tell if a number is an integer or a fractional.
    """
    # Accept python float or cupy scalar
    try:
        x_val = float(x)
    except Exception:
        # fallback: cp.ndarray scalar
        x_val = float(cp.asnumpy(x))
    return float(int(x_val)) != x_val

def faslt(input_data, Fs, F, c1, o, mult):
    """
    GPU-accelerated FASLT producing a real-valued superlet spectrum, following CPU code structure.

    Parameters:
    - input_data: [buffers x samples] matrix or 1D array
    - Fs: sampling frequency in Hz
    - F: frequency-of-interest array
    - c1: number of initial wavelet cycles
    - o: [1 x 2] interval of superresolution orders (optional, can be None or empty)
    - mult: specifies the use of multiplicative superresolution (0 - additive, != 0 - multiplicative)

    Returns:
    - wtresult: [frequencies x samples] superlet spectrum (numpy array)
    """
    if F is None or len(F) == 0:
        raise ValueError('frequencies not defined')
    if input_data is None or getattr(input_data, "size", 0) == 0:
        raise ValueError('input is empty')

    # Normalize input shape (host side), then move to GPU
    import numpy as _np
    if getattr(input_data, "ndim", 1) == 1:
        input_data = _np.reshape(input_data, (1, -1))
    elif input_data.ndim > 2:
        raise ValueError('Input must be 1D or 2D')

    Nbuffers, Npoints = input_data.shape

    # Prepare frequency and move to GPU
    F_gpu = cp.asarray(F, dtype=cp.float64)

    # Handle order parameter
    nF = int(len(F))
    if o is None or (hasattr(o, "__len__") and len(o) == 0):
        order_frac = cp.ones(nF, dtype=CP_FLOAT64)  # type: ignore[arg-type]
        order_int = order_frac
    else:
        if not (hasattr(o, "__len__") and len(o) == 2):
            raise ValueError('Order interval must be a 2-element array')
        o0 = float(o[0]); o1 = float(o[1])
        order_frac = cp.linspace(o0, o1, nF, dtype=CP_FLOAT64)  # type: ignore[arg-type]
        order_int = cp.ceil(order_frac)

    # Create wavelet sets
    wavelets = [[] for _ in range(nF)]
    padding = 0
    for i_freq in range(nF):
        Fc = float(F_gpu[i_freq].item())
        max_ord = int(order_int[i_freq].item())
        for i_ord in range(1, max_ord + 1):
            if mult != 0:
                n_cyc = i_ord * c1
            else:
                n_cyc = i_ord + c1
            wl = cxmorlet(Fc, n_cyc, float(Fs))
            wavelets[i_freq].append(wl)
            padding = max(padding, int(len(wl) // 2))

    # Buffers on GPU
    total_len = int(Npoints + 2 * padding)
    buffer = cp.zeros(total_len, dtype=CP_COMPLEX128)  # type: ignore[arg-type]
    wtresult = cp.zeros((nF, Npoints), dtype=CP_FLOAT64)  # type: ignore[arg-type]

    bufbegin = int(padding)
    bufend = int(padding + Npoints)

    # Copy input to GPU once
    input_gpu = cp.asarray(input_data, dtype=CP_FLOAT64)

    # Loop over buffers and freqs
    for i_buf in range(Nbuffers):
        # set central region (cast to complex to match conv dtype)
        buffer[bufbegin:bufend] = input_gpu[i_buf, :].astype(CP_COMPLEX128, copy=False)  # type: ignore[arg-type]
        for i_freq in range(nF):
            # Pooling buffer for geometric mean, starts with 1
            temp = cp.ones(Npoints, dtype=CP_FLOAT64)  # type: ignore[arg-type]

            # Number of integer wavelets
            n_wavelets = int(cp.floor(order_frac[i_freq]).item())

            # Convolve with each integer wavelet
            for i_ord in range(1, n_wavelets + 1):
                conv_full = fftconvolve(buffer, wavelets[i_freq][i_ord - 1], mode='same')
                # CPU version used energy term: 2 * |conv|^2
                band = conv_full[bufbegin:bufend]
                abs_band = cp.abs(band)
                temp *= (2.0 * abs_band**2)

            # Handle fractional part
            ord_frac_val = float(order_frac[i_freq])
            if is_fractional(ord_frac_val) and len(wavelets[i_freq]) > n_wavelets:
                i_ord = int(order_int[i_freq].item())
                exponent = ord_frac_val - float(cp.fix(order_frac[i_freq]).item())
                conv_full = fftconvolve(buffer, wavelets[i_freq][i_ord - 1], mode='same')
                band = conv_full[bufbegin:bufend]
                abs_band = cp.abs(band)
                temp *= (2.0 * abs_band**2) ** exponent

            # Geometric mean
            # guard against division by zero or extremely small orders
            of = float(order_frac[i_freq])
            if of < 1e-12:
                of = 1e-12
            root = 1.0 / of
            temp = cp.power(temp, root)

            wtresult[i_freq, :] += temp

    wtresult /= float(Nbuffers)
    return cp.asnumpy(wtresult)