import cupy as cp
from cupyx.scipy.signal import fftconvolve
from typing import Any

# Dtype aliases to avoid NameError and keep annotations concise
cp_float64 = cp.float64
cp_complex128 = cp.complex128
 
def fasltcx(input_data, Fs, F, c1, o, mult):
    """
    Modified FASLTCX to output complex spectrum on GPU (CuPy).
    Fixes scalar conversions, dtype consistency, and fractional-order handling.
    """
    # Validate inputs
    if F is None or len(F) == 0:
        raise ValueError('frequencies not defined')
    if input_data is None or input_data.size == 0:
        raise ValueError('input is empty')

    # Normalize shapes (host side) then move to GPU
    if input_data.ndim == 1:
        input_data = input_data.reshape(1, -1)
    elif input_data.ndim > 2:
        raise ValueError('Input must be 1D or 2D')

    Nbuffers, Npoints = input_data.shape

    # Move arrays to GPU
    input_data = cp.asarray(input_data)
    F_gpu = cp.asarray(F)

    # Handle order parameter on GPU
    nF = int(len(F))
    if o is None or (hasattr(o, '__len__') and len(o) == 0):
        order_frac = cp.ones(nF, dtype=cp.float64)  # type: ignore[arg-type]
        order_int = order_frac
    else:
        if not (hasattr(o, '__len__') and len(o) == 2):
            raise ValueError('Order interval must be a 2-element array')
        o0 = float(o[0])
        o1 = float(o[1])
        order_frac = cp.linspace(o0, o1, nF, dtype=cp.float64)  # type: ignore[arg-type]
        order_int = cp.ceil(order_frac)

    # Prepare wavelets per frequency
    wavelets = [[] for _ in range(nF)]
    padding = 0

    for i_freq in range(nF):
        # Convert scalar for Python usage
        max_ord = int(order_int[i_freq].item())
        Fc = float(F_gpu[i_freq].item())
        for i_ord in range(1, max_ord + 1):
            n_cyc = (i_ord * c1) if mult else (i_ord + c1)
            wavelet = cxmorlet(Fc, n_cyc, float(Fs))
            # Ensure device array complex128
            wl_gpu = cp.asarray(wavelet, dtype=cp.complex128)
            wavelets[i_freq].append(wl_gpu)
            # padding is half-length (integer)
            half_len = len(wavelet) // 2
            if half_len > padding:
                padding = half_len

    # Buffers
    total_len = int(Npoints + 2 * padding)
    # Pylance may misinfer dtype types; annotate for clarity
    buffer: cp.ndarray = cp.zeros(total_len, dtype=cp_complex128)  # type: ignore[arg-type]
    wtresult: cp.ndarray = cp.zeros((nF, Npoints), dtype=cp_complex128)  # type: ignore[arg-type]

    bufbegin = int(padding)
    bufend = int(padding + Npoints)

    # Loop over input buffers and frequencies
    for i_buf in range(Nbuffers):
        # Place current signal into central buffer slice
        # Cast to complex to match convolution dtype
        buffer[bufbegin:bufend] = input_data[i_buf, :].astype(cp.complex128, copy=False)

        for i_freq in range(nF):
            # Init magnitude and phase accumulators
            mag_buffer: cp.ndarray = cp.ones(Npoints, dtype=cp_float64)  # type: ignore[arg-type]
            phase_buffer: cp.ndarray = cp.zeros(Npoints, dtype=cp_complex128)  # type: ignore[arg-type]

            # Number of integer wavelets
            n_wavelets = int(cp.fix(order_frac[i_freq]))  # integer part
            # Convolve with each integer wavelet
            for i_ord in range(1, n_wavelets + 1):
                wl = wavelets[i_freq][i_ord - 1]
                conv_full = fftconvolve(buffer, wl, mode='same')
                conv = conv_full[bufbegin:bufend]
                abs_conv = cp.abs(conv)
                mag_buffer *= cp.maximum(abs_conv, 1e-300)
                phase_buffer += conv / (abs_conv + 1e-12)

            # Handle fractional part if needed
            # Ensure Python float to avoid .item() on potential float
            ord_frac_val = float(order_frac[i_freq])
            if is_fractional(ord_frac_val) and len(wavelets[i_freq]) > n_wavelets:
                i_ord = int(order_int[i_freq])
                exponent = float(ord_frac_val - float(cp.fix(order_frac[i_freq])))
                wl = wavelets[i_freq][i_ord - 1]
                conv_full = fftconvolve(buffer, wl, mode='same')
                conv = conv_full[bufbegin:bufend]
                abs_conv = cp.abs(conv)
                mag_buffer *= cp.power(cp.maximum(abs_conv, 1e-300), exponent)
                phase_buffer += (conv / (abs_conv + 1e-12)) * exponent

            # Geometric mean for magnitude, arithmetic for phase
            rfactor = 1.0 / float(order_frac[i_freq])
            magnitude = cp.power(mag_buffer, rfactor)
            phase = phase_buffer * rfactor  # phase_buffer already unit complex sum

            # Combine to complex
            wtresult[i_freq, :] += magnitude * phase

    # Average over buffers
    wtresult /= float(Nbuffers)

    return cp.asnumpy(wtresult)


def cxmorlet(Fc, Nc, Fs):
    """
    Computes the complex Morlet wavelet for the desired center frequency Fc with Nc cycles, with a sampling frequency Fs.
    Returns a CuPy array (complex128).
    """
    # Avoid zero frequency by setting minimum frequency to 0.001 in sign of Fc
    if abs(Fc) < 1e-10:
        Fc = 0.001 if Fc >= 0 else -0.001

    # Use CuPy for vectorized creation
    Fc_cp = cp.asarray(Fc, dtype=cp.float64)
    Nc_cp = cp.asarray(Nc, dtype=cp.float64)
    Fs_cp = cp.asarray(Fs, dtype=cp.float64)

    sd = (Nc_cp / 2.0) * (1.0 / cp.abs(Fc_cp)) / 2.5  # standard deviation (seconds)
    # Window length as odd integer ~ 12*sd*Fs rounded to nearest odd
    wl_float = 6.0 * sd * Fs_cp
    wl_even = int(2 * cp.floor(cp.fix(wl_float * 2).astype(cp.float64) / 2.0).item())
    wl = wl_even + 1  # make it odd
    w: cp.ndarray = cp.zeros(wl, dtype=cp_complex128)  # type: ignore[arg-type]

    off = wl // 2
    # Vectorized time axis
    i = cp.arange(wl, dtype=cp.float64)
    t = (i - off) / Fs_cp

    # Compute wavelet and normalization
    w = bw_cf(t, sd, Fc_cp)
    gi = gauss(t, sd).sum()
    w = w / gi
    return w


def bw_cf(t, bw, cf):
    """
    Computes complex wavelet coefficients (CuPy vectorized).
    """
    cnorm = 1.0 / (bw * cp.sqrt(2.0 * cp.pi))
    exp1 = cnorm * cp.exp(-(t**2) / (2.0 * bw**2))
    return cp.exp(2j * cp.pi * cf * t) * exp1


def gauss(t, sd):
    """
    Computes the Gaussian coefficient (CuPy vectorized).
    """
    cnorm = 1.0 / (sd * cp.sqrt(2.0 * cp.pi))
    return cnorm * cp.exp(-(t**2) / (2.0 * sd**2))


def is_fractional(x: float) -> bool:
    """
    Return True if x has a fractional part.
    """
    return float(int(x)) != float(x)