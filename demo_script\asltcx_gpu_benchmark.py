#!/usr/bin/env python3
"""
Performance Benchmarking Module for ASLTCX GPU Implementation

This module provides comprehensive performance benchmarking capabilities for comparing
GPU and CPU implementations of ASLTCX, including memory usage tracking, scalability
analysis, and detailed performance profiling.
"""

import numpy as np
import time
import sys
import os
import psutil
from typing import Dict, List, Tuple, Optional, Any, Callable
import warnings

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

# Check GPU availability
GPU_AVAILABLE = False
try:
    import cupy as cp
    device_count = cp.cuda.runtime.getDeviceCount()
    if device_count > 0:
        GPU_AVAILABLE = True
except:
    pass


class PerformanceProfiler:
    """Comprehensive performance profiler for CPU and GPU implementations."""
    
    def __init__(self):
        self.cpu_memory_start = 0
        self.gpu_memory_start = 0
        self.measurements = []
        
    def start_profiling(self):
        """Start performance profiling."""
        # CPU memory
        process = psutil.Process()
        self.cpu_memory_start = process.memory_info().rss / (1024**2)  # MB
        
        # GPU memory
        if GPU_AVAILABLE:
            self.gpu_memory_start = cp.cuda.runtime.memGetInfo()[0] / (1024**2)  # MB free
        
        self.measurements = []
    
    def record_measurement(self, label: str, execution_time: float, 
                          additional_info: Optional[Dict] = None):
        """Record a performance measurement."""
        measurement = {
            'label': label,
            'execution_time': execution_time,
            'timestamp': time.time()
        }
        
        # CPU memory usage
        process = psutil.Process()
        current_cpu_memory = process.memory_info().rss / (1024**2)
        measurement['cpu_memory_mb'] = current_cpu_memory - self.cpu_memory_start
        
        # GPU memory usage
        if GPU_AVAILABLE:
            current_gpu_free = cp.cuda.runtime.memGetInfo()[0] / (1024**2)
            measurement['gpu_memory_used_mb'] = self.gpu_memory_start - current_gpu_free
        
        if additional_info:
            measurement.update(additional_info)
        
        self.measurements.append(measurement)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        if not self.measurements:
            return {}
        
        cpu_times = [m['execution_time'] for m in self.measurements if 'cpu' in m['label'].lower()]
        gpu_times = [m['execution_time'] for m in self.measurements if 'gpu' in m['label'].lower()]
        
        summary = {
            'total_measurements': len(self.measurements),
            'cpu_measurements': len(cpu_times),
            'gpu_measurements': len(gpu_times)
        }
        
        if cpu_times:
            summary['cpu_stats'] = {
                'mean_time': np.mean(cpu_times),
                'std_time': np.std(cpu_times),
                'min_time': np.min(cpu_times),
                'max_time': np.max(cpu_times)
            }
        
        if gpu_times:
            summary['gpu_stats'] = {
                'mean_time': np.mean(gpu_times),
                'std_time': np.std(gpu_times),
                'min_time': np.min(gpu_times),
                'max_time': np.max(gpu_times)
            }
            
            if cpu_times:
                summary['speedup'] = np.mean(cpu_times) / np.mean(gpu_times)
        
        return summary


def benchmark_scalability(cpu_func: Callable, gpu_func: Optional[Callable],
                         base_signal: np.ndarray, fs: float, freqs: np.ndarray,
                         ncyc: int, orders: Tuple[int, int],
                         scale_factors: List[float] = [0.5, 1.0, 2.0, 4.0]) -> Dict[str, Any]:
    """
    Benchmark scalability with different signal lengths.
    
    Args:
        cpu_func: CPU implementation function
        gpu_func: GPU implementation function (None if not available)
        base_signal: Base signal to scale
        fs: Sampling frequency
        freqs: Frequency array
        ncyc: Number of cycles
        orders: Order range
        scale_factors: Factors to scale signal length
    
    Returns:
        Dictionary with scalability results
    """
    print("Running scalability benchmark...")
    
    results = {
        'scale_factors': scale_factors,
        'cpu_results': {},
        'gpu_results': {},
        'parameters': {
            'base_length': len(base_signal),
            'fs': fs,
            'n_freqs': len(freqs),
            'ncyc': ncyc,
            'orders': orders
        }
    }
    
    profiler = PerformanceProfiler()
    
    for scale_factor in scale_factors:
        print(f"  Testing scale factor {scale_factor}x...")
        
        # Scale signal
        if scale_factor == 1.0:
            test_signal = base_signal.copy()
        elif scale_factor < 1.0:
            # Downsample
            step = int(1.0 / scale_factor)
            test_signal = base_signal[::step]
        else:
            # Upsample by repeating
            repeat_factor = int(scale_factor)
            test_signal = np.repeat(base_signal, repeat_factor)
        
        signal_length = len(test_signal)
        print(f"    Signal length: {signal_length}")
        
        profiler.start_profiling()
        
        # CPU benchmark
        try:
            start_time = time.perf_counter()
            cpu_result = cpu_func(test_signal, fs, freqs, ncyc, orders, False)
            cpu_time = time.perf_counter() - start_time
            
            profiler.record_measurement(f"CPU_{scale_factor}x", cpu_time, {
                'signal_length': signal_length,
                'scale_factor': scale_factor,
                'result_shape': cpu_result.shape
            })
            
            results['cpu_results'][scale_factor] = {
                'time': cpu_time,
                'signal_length': signal_length,
                'throughput_samples_per_sec': signal_length / cpu_time,
                'memory_mb': profiler.measurements[-1]['cpu_memory_mb']
            }
            
            print(f"    CPU: {cpu_time:.4f}s ({signal_length/cpu_time:.0f} samples/s)")
            
        except Exception as e:
            print(f"    CPU failed: {e}")
            results['cpu_results'][scale_factor] = {'error': str(e)}
        
        # GPU benchmark
        if gpu_func and GPU_AVAILABLE:
            try:
                # Warm up GPU
                _ = gpu_func(test_signal[:100], fs, freqs[:10], ncyc, (1, 2), False)
                
                start_time = time.perf_counter()
                gpu_result = gpu_func(test_signal, fs, freqs, ncyc, orders, False)
                gpu_time = time.perf_counter() - start_time
                
                profiler.record_measurement(f"GPU_{scale_factor}x", gpu_time, {
                    'signal_length': signal_length,
                    'scale_factor': scale_factor,
                    'result_shape': gpu_result.shape
                })
                
                results['gpu_results'][scale_factor] = {
                    'time': gpu_time,
                    'signal_length': signal_length,
                    'throughput_samples_per_sec': signal_length / gpu_time,
                    'memory_mb': profiler.measurements[-1].get('gpu_memory_used_mb', 0),
                    'speedup': cpu_time / gpu_time if 'cpu_results' in results and scale_factor in results['cpu_results'] else None
                }
                
                speedup = cpu_time / gpu_time if scale_factor in results['cpu_results'] and 'time' in results['cpu_results'][scale_factor] else 0
                print(f"    GPU: {gpu_time:.4f}s ({signal_length/gpu_time:.0f} samples/s, {speedup:.2f}x speedup)")
                
            except Exception as e:
                print(f"    GPU failed: {e}")
                results['gpu_results'][scale_factor] = {'error': str(e)}
    
    # Calculate scaling efficiency
    if len(results['cpu_results']) > 1:
        base_cpu_time = results['cpu_results'].get(1.0, {}).get('time')
        if base_cpu_time:
            for scale_factor, result in results['cpu_results'].items():
                if 'time' in result and scale_factor != 1.0:
                    expected_time = base_cpu_time * scale_factor
                    actual_time = result['time']
                    result['scaling_efficiency'] = expected_time / actual_time
    
    if len(results['gpu_results']) > 1:
        base_gpu_time = results['gpu_results'].get(1.0, {}).get('time')
        if base_gpu_time:
            for scale_factor, result in results['gpu_results'].items():
                if 'time' in result and scale_factor != 1.0:
                    expected_time = base_gpu_time * scale_factor
                    actual_time = result['time']
                    result['scaling_efficiency'] = expected_time / actual_time
    
    return results


def benchmark_frequency_scaling(cpu_func: Callable, gpu_func: Optional[Callable],
                               signal: np.ndarray, fs: float, base_freqs: np.ndarray,
                               ncyc: int, orders: Tuple[int, int],
                               freq_scales: List[float] = [0.5, 1.0, 2.0, 4.0]) -> Dict[str, Any]:
    """
    Benchmark performance with different frequency array sizes.
    
    Args:
        cpu_func: CPU implementation function
        gpu_func: GPU implementation function
        signal: Input signal
        fs: Sampling frequency
        base_freqs: Base frequency array
        ncyc: Number of cycles
        orders: Order range
        freq_scales: Factors to scale frequency array size
    
    Returns:
        Dictionary with frequency scaling results
    """
    print("Running frequency scaling benchmark...")
    
    results = {
        'freq_scales': freq_scales,
        'cpu_results': {},
        'gpu_results': {},
        'parameters': {
            'signal_length': len(signal),
            'base_n_freqs': len(base_freqs),
            'fs': fs,
            'ncyc': ncyc,
            'orders': orders
        }
    }
    
    for freq_scale in freq_scales:
        print(f"  Testing frequency scale {freq_scale}x...")
        
        # Scale frequency array
        n_freqs = max(1, int(len(base_freqs) * freq_scale))
        test_freqs = np.linspace(base_freqs[0], base_freqs[-1], n_freqs)
        
        print(f"    Number of frequencies: {n_freqs}")
        
        # CPU benchmark
        try:
            start_time = time.perf_counter()
            cpu_result = cpu_func(signal, fs, test_freqs, ncyc, orders, False)
            cpu_time = time.perf_counter() - start_time
            
            results['cpu_results'][freq_scale] = {
                'time': cpu_time,
                'n_freqs': n_freqs,
                'time_per_freq': cpu_time / n_freqs,
                'result_shape': cpu_result.shape
            }
            
            print(f"    CPU: {cpu_time:.4f}s ({cpu_time/n_freqs*1000:.2f}ms per freq)")
            
        except Exception as e:
            print(f"    CPU failed: {e}")
            results['cpu_results'][freq_scale] = {'error': str(e)}
        
        # GPU benchmark
        if gpu_func and GPU_AVAILABLE:
            try:
                start_time = time.perf_counter()
                gpu_result = gpu_func(signal, fs, test_freqs, ncyc, orders, False)
                gpu_time = time.perf_counter() - start_time
                
                results['gpu_results'][freq_scale] = {
                    'time': gpu_time,
                    'n_freqs': n_freqs,
                    'time_per_freq': gpu_time / n_freqs,
                    'result_shape': gpu_result.shape,
                    'speedup': cpu_time / gpu_time if freq_scale in results['cpu_results'] and 'time' in results['cpu_results'][freq_scale] else None
                }
                
                speedup = cpu_time / gpu_time if freq_scale in results['cpu_results'] and 'time' in results['cpu_results'][freq_scale] else 0
                print(f"    GPU: {gpu_time:.4f}s ({gpu_time/n_freqs*1000:.2f}ms per freq, {speedup:.2f}x speedup)")
                
            except Exception as e:
                print(f"    GPU failed: {e}")
                results['gpu_results'][freq_scale] = {'error': str(e)}
    
    return results


def print_benchmark_summary(scalability_results: Dict[str, Any], 
                          frequency_results: Dict[str, Any]):
    """Print comprehensive benchmark summary."""
    print("\n" + "="*60)
    print("PERFORMANCE BENCHMARK SUMMARY")
    print("="*60)
    
    # Scalability results
    print("\nScalability Benchmark:")
    print("  Scale Factor | CPU Time (s) | GPU Time (s) | Speedup | CPU Efficiency | GPU Efficiency")
    print("  " + "-"*80)
    
    for scale_factor in scalability_results['scale_factors']:
        cpu_result = scalability_results['cpu_results'].get(scale_factor, {})
        gpu_result = scalability_results['gpu_results'].get(scale_factor, {})
        
        cpu_time = cpu_result.get('time', 'N/A')
        gpu_time = gpu_result.get('time', 'N/A')
        speedup = gpu_result.get('speedup', 'N/A')
        cpu_eff = cpu_result.get('scaling_efficiency', 'N/A')
        gpu_eff = gpu_result.get('scaling_efficiency', 'N/A')
        
        if isinstance(cpu_time, float):
            cpu_time = f"{cpu_time:.4f}"
        if isinstance(gpu_time, float):
            gpu_time = f"{gpu_time:.4f}"
        if isinstance(speedup, float):
            speedup = f"{speedup:.2f}x"
        if isinstance(cpu_eff, float):
            cpu_eff = f"{cpu_eff:.2f}"
        if isinstance(gpu_eff, float):
            gpu_eff = f"{gpu_eff:.2f}"
        
        print(f"  {scale_factor:>11.1f}x | {cpu_time:>11} | {gpu_time:>11} | {speedup:>7} | {cpu_eff:>13} | {gpu_eff:>13}")
    
    # Frequency scaling results
    print("\nFrequency Scaling Benchmark:")
    print("  Freq Scale | N Freqs | CPU Time (s) | GPU Time (s) | Speedup | CPU ms/freq | GPU ms/freq")
    print("  " + "-"*80)
    
    for freq_scale in frequency_results['freq_scales']:
        cpu_result = frequency_results['cpu_results'].get(freq_scale, {})
        gpu_result = frequency_results['gpu_results'].get(freq_scale, {})
        
        n_freqs = cpu_result.get('n_freqs', gpu_result.get('n_freqs', 'N/A'))
        cpu_time = cpu_result.get('time', 'N/A')
        gpu_time = gpu_result.get('time', 'N/A')
        speedup = gpu_result.get('speedup', 'N/A')
        cpu_per_freq = cpu_result.get('time_per_freq', 0) * 1000 if 'time_per_freq' in cpu_result else 'N/A'
        gpu_per_freq = gpu_result.get('time_per_freq', 0) * 1000 if 'time_per_freq' in gpu_result else 'N/A'
        
        if isinstance(cpu_time, float):
            cpu_time = f"{cpu_time:.4f}"
        if isinstance(gpu_time, float):
            gpu_time = f"{gpu_time:.4f}"
        if isinstance(speedup, float):
            speedup = f"{speedup:.2f}x"
        if isinstance(cpu_per_freq, float):
            cpu_per_freq = f"{cpu_per_freq:.2f}"
        if isinstance(gpu_per_freq, float):
            gpu_per_freq = f"{gpu_per_freq:.2f}"
        
        print(f"  {freq_scale:>9.1f}x | {n_freqs:>7} | {cpu_time:>11} | {gpu_time:>11} | {speedup:>7} | {cpu_per_freq:>10} | {gpu_per_freq:>10}")
    
    print("="*60)
