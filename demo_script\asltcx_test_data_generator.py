#!/usr/bin/env python3
"""
Comprehensive Test Data Generator for ASLTCX GPU Testing

This module generates realistic synthetic seismic-like data for comprehensive testing
of GPU-accelerated ASLTCX implementations. The generated data mimics the complexity
and characteristics of real seismic data.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any, Union
import warnings


class SeismicDataGenerator:
    """Generator for realistic synthetic seismic data."""
    
    def __init__(self, fs: float = 1000.0, random_seed: Optional[int] = None):
        """
        Initialize the seismic data generator.
        
        Args:
            fs: Sampling frequency in Hz
            random_seed: Random seed for reproducible results
        """
        self.fs = fs
        self.dt = 1.0 / fs
        if random_seed is not None:
            np.random.seed(random_seed)
    
    def generate_wavelet(self, center_freq: float, duration: float, 
                        wavelet_type: str = 'ricker') -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate a seismic wavelet.
        
        Args:
            center_freq: Center frequency in Hz
            duration: Wavelet duration in seconds
            wavelet_type: Type of wavelet ('ricker', 'morlet', 'klauder')
        
        Returns:
            Tuple of (time_array, wavelet)
        """
        n_samples = int(duration * self.fs)
        t = np.linspace(-duration/2, duration/2, n_samples)
        
        if wavelet_type == 'ricker':
            # Ricker wavelet (Mexican hat)
            a = np.pi * center_freq * t
            wavelet = (1 - 2*a**2) * np.exp(-a**2)
            
        elif wavelet_type == 'morlet':
            # Morlet wavelet
            sigma = 1.0 / (2 * np.pi * center_freq)
            wavelet = np.exp(2j * np.pi * center_freq * t) * np.exp(-t**2 / (2*sigma**2))
            wavelet = np.real(wavelet)  # Take real part for seismic data
            
        elif wavelet_type == 'klauder':
            # Klauder wavelet (linear chirp)
            f1 = center_freq * 0.5
            f2 = center_freq * 1.5
            wavelet = np.sin(2 * np.pi * (f1 + (f2-f1) * (t + duration/2) / duration) * t)
            # Apply Gaussian envelope
            wavelet *= np.exp(-t**2 / (2*(duration/6)**2))
            
        else:
            raise ValueError(f"Unknown wavelet type: {wavelet_type}")
        
        return t, wavelet
    
    def generate_reflection_series(self, n_samples: int, n_reflectors: int = 10,
                                 amplitude_range: Tuple[float, float] = (-1.0, 1.0)) -> np.ndarray:
        """
        Generate a sparse reflection coefficient series.
        
        Args:
            n_samples: Number of samples
            n_reflectors: Number of reflectors
            amplitude_range: Range of reflection coefficients
        
        Returns:
            Reflection coefficient series
        """
        reflectivity = np.zeros(n_samples)
        
        # Random reflector positions (avoid edges)
        positions = np.random.randint(int(0.1*n_samples), int(0.9*n_samples), n_reflectors)
        
        # Random amplitudes
        amplitudes = np.random.uniform(amplitude_range[0], amplitude_range[1], n_reflectors)
        
        reflectivity[positions] = amplitudes
        
        return reflectivity
    
    def add_noise(self, signal: np.ndarray, snr_db: float = 20.0,
                  noise_type: str = 'white') -> np.ndarray:
        """
        Add noise to signal.
        
        Args:
            signal: Input signal
            snr_db: Signal-to-noise ratio in dB
            noise_type: Type of noise ('white', 'colored', 'impulsive')
        
        Returns:
            Noisy signal
        """
        signal_power = np.mean(signal**2)
        noise_power = signal_power / (10**(snr_db/10))
        
        if noise_type == 'white':
            noise = np.random.normal(0, np.sqrt(noise_power), len(signal))
            
        elif noise_type == 'colored':
            # Generate colored noise using AR process
            white_noise = np.random.normal(0, 1, len(signal))
            # Simple AR(1) filter for colored noise
            noise = np.zeros_like(white_noise)
            alpha = 0.8
            for i in range(1, len(noise)):
                noise[i] = alpha * noise[i-1] + white_noise[i]
            noise *= np.sqrt(noise_power / np.mean(noise**2))
            
        elif noise_type == 'impulsive':
            # Impulsive noise (sparse high-amplitude spikes)
            noise = np.random.normal(0, np.sqrt(noise_power*0.1), len(signal))
            n_spikes = max(1, len(signal) // 100)
            spike_positions = np.random.randint(0, len(signal), n_spikes)
            spike_amplitudes = np.random.normal(0, np.sqrt(noise_power*10), n_spikes)
            noise[spike_positions] += spike_amplitudes
            
        else:
            raise ValueError(f"Unknown noise type: {noise_type}")
        
        return signal + noise
    
    def apply_attenuation(self, signal: np.ndarray, time_array: np.ndarray,
                         q_factor: float = 50.0, reference_freq: float = 30.0) -> np.ndarray:
        """
        Apply frequency-dependent attenuation (Q-factor).
        
        Args:
            signal: Input signal
            time_array: Time array
            q_factor: Quality factor
            reference_freq: Reference frequency in Hz
        
        Returns:
            Attenuated signal
        """
        # Simple exponential decay with time
        decay = np.exp(-time_array * reference_freq / q_factor)
        return signal * decay
    
    def generate_complex_trace(self, duration: float, trace_config: Dict[str, Any]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate a complex synthetic seismic trace.
        
        Args:
            duration: Trace duration in seconds
            trace_config: Configuration dictionary with trace parameters
        
        Returns:
            Tuple of (time_array, trace_data)
        """
        n_samples = int(duration * self.fs)
        t = np.linspace(0, duration, n_samples)
        trace = np.zeros(n_samples)
        
        # Extract configuration parameters
        wavelets = trace_config.get('wavelets', [{'freq': 30, 'type': 'ricker', 'amplitude': 1.0}])
        reflectors = trace_config.get('reflectors', {'count': 10, 'amplitude_range': (-0.5, 0.5)})
        noise_config = trace_config.get('noise', {'snr_db': 20, 'type': 'white'})
        attenuation = trace_config.get('attenuation', {'q_factor': 50, 'reference_freq': 30})
        
        # Generate each wavelet component
        for wavelet_config in wavelets:
            freq = wavelet_config['freq']
            wtype = wavelet_config.get('type', 'ricker')
            amplitude = wavelet_config.get('amplitude', 1.0)
            
            # Generate wavelet
            wavelet_duration = min(0.2, duration/5)  # Limit wavelet duration
            _, wavelet = self.generate_wavelet(freq, wavelet_duration, wtype)
            
            # Generate reflectivity series
            reflectivity = self.generate_reflection_series(
                n_samples, 
                reflectors['count'],
                reflectors['amplitude_range']
            )
            
            # Convolve wavelet with reflectivity
            component = np.convolve(reflectivity, wavelet, mode='same') * amplitude
            trace += component
        
        # Apply attenuation
        if attenuation:
            trace = self.apply_attenuation(trace, t, 
                                         attenuation['q_factor'],
                                         attenuation['reference_freq'])
        
        # Add noise
        if noise_config:
            trace = self.add_noise(trace, 
                                 noise_config['snr_db'],
                                 noise_config['type'])
        
        return t, trace
    
    def generate_test_suite(self, duration: float = 2.0, n_traces: int = 5) -> Dict[str, Any]:
        """
        Generate a comprehensive test suite with various trace types.
        
        Args:
            duration: Duration of each trace in seconds
            n_traces: Number of traces to generate
        
        Returns:
            Dictionary containing test suite data
        """
        test_suite = {
            'parameters': {
                'duration': duration,
                'fs': self.fs,
                'dt': self.dt,
                'n_traces': n_traces
            },
            'traces': {},
            'time_array': np.linspace(0, duration, int(duration * self.fs))
        }
        
        # Define different trace configurations
        trace_configs = [
            {
                'name': 'simple_reflection',
                'description': 'Simple reflection with single wavelet',
                'config': {
                    'wavelets': [{'freq': 30, 'type': 'ricker', 'amplitude': 1.0}],
                    'reflectors': {'count': 8, 'amplitude_range': (-0.8, 0.8)},
                    'noise': {'snr_db': 25, 'type': 'white'},
                    'attenuation': {'q_factor': 60, 'reference_freq': 30}
                }
            },
            {
                'name': 'multi_frequency',
                'description': 'Multiple frequency components',
                'config': {
                    'wavelets': [
                        {'freq': 15, 'type': 'ricker', 'amplitude': 0.6},
                        {'freq': 40, 'type': 'ricker', 'amplitude': 0.8},
                        {'freq': 70, 'type': 'ricker', 'amplitude': 0.4}
                    ],
                    'reflectors': {'count': 12, 'amplitude_range': (-0.6, 0.6)},
                    'noise': {'snr_db': 20, 'type': 'white'},
                    'attenuation': {'q_factor': 45, 'reference_freq': 35}
                }
            },
            {
                'name': 'dispersive_wave',
                'description': 'Dispersive wave with chirp',
                'config': {
                    'wavelets': [
                        {'freq': 25, 'type': 'klauder', 'amplitude': 1.0},
                        {'freq': 50, 'type': 'morlet', 'amplitude': 0.5}
                    ],
                    'reflectors': {'count': 6, 'amplitude_range': (-1.0, 1.0)},
                    'noise': {'snr_db': 18, 'type': 'colored'},
                    'attenuation': {'q_factor': 35, 'reference_freq': 25}
                }
            },
            {
                'name': 'noisy_data',
                'description': 'High noise environment',
                'config': {
                    'wavelets': [{'freq': 35, 'type': 'ricker', 'amplitude': 1.2}],
                    'reflectors': {'count': 15, 'amplitude_range': (-0.4, 0.4)},
                    'noise': {'snr_db': 10, 'type': 'impulsive'},
                    'attenuation': {'q_factor': 30, 'reference_freq': 35}
                }
            },
            {
                'name': 'high_frequency',
                'description': 'High frequency content',
                'config': {
                    'wavelets': [
                        {'freq': 80, 'type': 'ricker', 'amplitude': 0.8},
                        {'freq': 120, 'type': 'morlet', 'amplitude': 0.6}
                    ],
                    'reflectors': {'count': 20, 'amplitude_range': (-0.3, 0.3)},
                    'noise': {'snr_db': 22, 'type': 'white'},
                    'attenuation': {'q_factor': 25, 'reference_freq': 80}
                }
            }
        ]
        
        # Generate traces
        for i in range(n_traces):
            config_idx = i % len(trace_configs)
            trace_config = trace_configs[config_idx]
            
            trace_name = f"{trace_config['name']}_{i+1}"
            
            # Generate trace
            time_array, trace_data = self.generate_complex_trace(duration, trace_config['config'])
            
            test_suite['traces'][trace_name] = {
                'data': trace_data,
                'config': trace_config['config'],
                'description': trace_config['description'],
                'statistics': {
                    'mean': np.mean(trace_data),
                    'std': np.std(trace_data),
                    'min': np.min(trace_data),
                    'max': np.max(trace_data),
                    'energy': np.sum(trace_data**2)
                }
            }
        
        return test_suite
    
    def plot_test_suite(self, test_suite: Dict[str, Any], save_path: Optional[str] = None):
        """
        Plot the generated test suite.
        
        Args:
            test_suite: Test suite data
            save_path: Optional path to save the plot
        """
        n_traces = len(test_suite['traces'])
        time_array = test_suite['time_array']
        
        fig, axes = plt.subplots(n_traces, 1, figsize=(12, 2*n_traces), sharex=True)
        if n_traces == 1:
            axes = [axes]
        
        fig.suptitle('Synthetic Seismic Test Data Suite', fontsize=16)
        
        for i, (trace_name, trace_info) in enumerate(test_suite['traces'].items()):
            trace_data = trace_info['data']
            description = trace_info['description']
            
            axes[i].plot(time_array, trace_data, 'k-', linewidth=0.8)
            axes[i].set_title(f'{trace_name}: {description}')
            axes[i].set_ylabel('Amplitude')
            axes[i].grid(True, alpha=0.3)
            
            # Add statistics text
            stats = trace_info['statistics']
            stats_text = f"μ={stats['mean']:.3f}, σ={stats['std']:.3f}"
            axes[i].text(0.02, 0.95, stats_text, transform=axes[i].transAxes,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        axes[-1].set_xlabel('Time (s)')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Test suite plot saved to: {save_path}")
        
        return fig


def generate_standard_test_data(duration: float = 2.0, fs: float = 1000.0, 
                              n_traces: int = 3, random_seed: int = 42) -> Dict[str, Any]:
    """
    Generate standard test data for ASLTCX GPU testing.
    
    Args:
        duration: Duration in seconds
        fs: Sampling frequency in Hz
        n_traces: Number of traces
        random_seed: Random seed for reproducibility
    
    Returns:
        Dictionary with test data
    """
    generator = SeismicDataGenerator(fs=fs, random_seed=random_seed)
    test_suite = generator.generate_test_suite(duration=duration, n_traces=n_traces)
    
    # Convert to format expected by ASLTCX functions
    trace_data = []
    for trace_name, trace_info in test_suite['traces'].items():
        trace_data.append(trace_info['data'])
    
    data_array = np.array(trace_data)
    time_array = test_suite['time_array']
    dt = 1.0 / fs
    
    return {
        'data': data_array,
        'time': time_array,
        'dt': dt,
        'fs': fs,
        'test_suite': test_suite
    }


if __name__ == "__main__":
    # Generate and plot test data when module is executed directly
    print("Generating synthetic seismic test data...")
    
    generator = SeismicDataGenerator(fs=1000.0, random_seed=42)
    test_suite = generator.generate_test_suite(duration=2.0, n_traces=5)
    
    print(f"Generated {len(test_suite['traces'])} test traces:")
    for trace_name, trace_info in test_suite['traces'].items():
        stats = trace_info['statistics']
        print(f"  {trace_name}: {trace_info['description']}")
        print(f"    Range: [{stats['min']:.3f}, {stats['max']:.3f}], Energy: {stats['energy']:.2e}")
    
    # Plot test suite
    fig = generator.plot_test_suite(test_suite, 'synthetic_test_data_suite.png')
    plt.show()
    
    print("Test data generation complete!")
