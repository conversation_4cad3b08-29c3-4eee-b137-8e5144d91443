#!/usr/bin/env python3
"""
Comprehensive GPU-Accelerated Test Case for ASLTCX Implementation

This test case mirrors the functionality of segy_asltcx_dlogst_comparison.py but specifically
tests the GPU implementation (asltcx_gpu.py) with the following features:

1. GPU vs CPU validation - Ensures GPU implementation produces equivalent results
2. Performance benchmarking - Demonstrates GPU acceleration benefits
3. Memory management - Proper GPU memory handling and monitoring
4. Error handling - Comprehensive GPU-specific error handling
5. Synthetic data generation - Complex test data matching real seismic data characteristics

Requirements:
- CuPy with CUDA-capable GPU
- All standard dependencies from requirements.txt
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os
import time
import warnings
from typing import Dict, List, Tuple, Optional, Any
import traceback

# Add the code and reference directories to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
reference_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'reference')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)
if reference_dir not in sys.path:
    sys.path.insert(0, reference_dir)

# GPU availability check and imports
GPU_AVAILABLE = False
try:
    import cupy as cp
    # Test GPU availability
    device_count = cp.cuda.runtime.getDeviceCount()
    if device_count > 0:
        # Test basic GPU operation
        test_array = cp.array([1, 2, 3])
        _ = cp.sum(test_array)
        GPU_AVAILABLE = True
        print(f"✓ GPU available: {device_count} CUDA device(s) detected")
        
        # Print GPU info
        device = cp.cuda.Device()
        print(f"  Active GPU: {device.id}")
        meminfo = cp.cuda.runtime.memGetInfo()
        total_mem_gb = meminfo[1] / (1024**3)
        free_mem_gb = meminfo[0] / (1024**3)
        print(f"  GPU Memory: {free_mem_gb:.1f}GB free / {total_mem_gb:.1f}GB total")
    else:
        print("✗ No CUDA devices found")
except Exception as e:
    print(f"✗ GPU not available: {e}")

# Import CPU and GPU implementations
try:
    from asltcx import asltcx as asltcx_cpu
    print("✓ CPU ASLTCX imported successfully")
except ImportError as e:
    print(f"✗ Failed to import CPU ASLTCX: {e}")
    sys.exit(1)

if GPU_AVAILABLE:
    try:
        from asltcx_gpu import asltcx as asltcx_gpu
        print("✓ GPU ASLTCX imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import GPU ASLTCX: {e}")
        GPU_AVAILABLE = False

# Import reference implementation for comparison
try:
    from dlogst_spec_descriptor import dlogst_spec_descriptor
    print("✓ DLOGST reference imported successfully")
except ImportError as e:
    print(f"✗ Failed to import DLOGST reference: {e}")
    sys.exit(1)


class GPUMemoryMonitor:
    """Monitor GPU memory usage during computations."""
    
    def __init__(self):
        self.initial_memory = None
        self.peak_memory = 0
        self.measurements = []
    
    def start_monitoring(self):
        """Start memory monitoring."""
        if GPU_AVAILABLE:
            self.initial_memory = cp.cuda.runtime.memGetInfo()[0]
            self.peak_memory = 0
            self.measurements = []
    
    def record_memory(self, label: str = ""):
        """Record current memory usage."""
        if GPU_AVAILABLE:
            current_free = cp.cuda.runtime.memGetInfo()[0]
            used_memory = self.initial_memory - current_free
            self.peak_memory = max(self.peak_memory, used_memory)
            self.measurements.append({
                'label': label,
                'used_mb': used_memory / (1024**2),
                'free_mb': current_free / (1024**2)
            })
    
    def get_peak_usage_mb(self) -> float:
        """Get peak memory usage in MB."""
        return self.peak_memory / (1024**2)
    
    def print_summary(self):
        """Print memory usage summary."""
        if GPU_AVAILABLE and self.measurements:
            print("\n--- GPU Memory Usage Summary ---")
            for measurement in self.measurements:
                print(f"  {measurement['label']}: {measurement['used_mb']:.1f}MB used, "
                      f"{measurement['free_mb']:.1f}MB free")
            print(f"  Peak usage: {self.get_peak_usage_mb():.1f}MB")
            print("--------------------------------")


def generate_complex_synthetic_data(duration: float = 2.0, fs: float = 1000.0, 
                                   num_traces: int = 3) -> Tuple[np.ndarray, np.ndarray, float]:
    """
    Generate complex synthetic seismic-like data for comprehensive testing.
    
    Args:
        duration: Signal duration in seconds
        fs: Sampling frequency in Hz
        num_traces: Number of traces to generate
    
    Returns:
        Tuple of (data, time_array, dt)
    """
    t = np.linspace(0, duration, int(fs * duration))
    dt = 1.0 / fs
    
    # Create multiple traces with different characteristics
    traces = []
    
    for i in range(num_traces):
        # Base signal with multiple frequency components
        signal = np.zeros_like(t)
        
        # Low frequency component (geological structure)
        signal += 0.8 * np.sin(2 * np.pi * 5 * t) * np.exp(-t/1.5)
        
        # Medium frequency burst (reflection event)
        burst_start = 0.3 + i * 0.2
        burst_end = burst_start + 0.4
        burst_mask = (t >= burst_start) & (t <= burst_end)
        signal += 0.6 * np.sin(2 * np.pi * (25 + i*5) * t) * burst_mask
        
        # High frequency component (noise/scattering)
        signal += 0.3 * np.sin(2 * np.pi * (60 + i*10) * t) * np.exp(-(t-1)**2/0.2)
        
        # Chirp signal (dispersive wave)
        f0, f1 = 10, 80
        chirp_t = t[t <= 1.0]
        if len(chirp_t) > 0:
            chirp = 0.4 * np.sin(2 * np.pi * (f0 + (f1-f0) * chirp_t / 1.0) * chirp_t)
            signal[:len(chirp)] += chirp * np.exp(-chirp_t/0.8)
        
        # Add realistic noise
        noise_level = 0.1 + i * 0.05
        signal += noise_level * np.random.randn(len(t))
        
        # Apply amplitude decay (geometric spreading)
        signal *= np.exp(-t/3.0)
        
        traces.append(signal)
    
    data = np.array(traces)
    return data, t, dt


def validate_gpu_cpu_equivalence(cpu_result: np.ndarray, gpu_result: np.ndarray, 
                                tolerance: float = 1e-10) -> Dict[str, Any]:
    """
    Validate that GPU and CPU implementations produce equivalent results.
    
    Args:
        cpu_result: Result from CPU implementation
        gpu_result: Result from GPU implementation  
        tolerance: Numerical tolerance for comparison
    
    Returns:
        Dictionary with validation results
    """
    validation = {
        'shapes_match': cpu_result.shape == gpu_result.shape,
        'max_abs_diff': 0.0,
        'mean_abs_diff': 0.0,
        'relative_error': 0.0,
        'passed': False,
        'details': []
    }
    
    if not validation['shapes_match']:
        validation['details'].append(f"Shape mismatch: CPU {cpu_result.shape} vs GPU {gpu_result.shape}")
        return validation
    
    # Calculate differences
    abs_diff = np.abs(cpu_result - gpu_result)
    validation['max_abs_diff'] = np.max(abs_diff)
    validation['mean_abs_diff'] = np.mean(abs_diff)
    
    # Calculate relative error
    cpu_magnitude = np.abs(cpu_result)
    nonzero_mask = cpu_magnitude > 1e-15
    if np.any(nonzero_mask):
        relative_errors = abs_diff[nonzero_mask] / cpu_magnitude[nonzero_mask]
        validation['relative_error'] = np.max(relative_errors)
    
    # Check if validation passes
    validation['passed'] = (validation['max_abs_diff'] < tolerance and 
                           validation['relative_error'] < tolerance * 100)
    
    if validation['passed']:
        validation['details'].append("✓ GPU and CPU results are equivalent within tolerance")
    else:
        validation['details'].append(f"✗ Results differ beyond tolerance ({tolerance})")
        validation['details'].append(f"  Max absolute difference: {validation['max_abs_diff']:.2e}")
        validation['details'].append(f"  Max relative error: {validation['relative_error']:.2e}")
    
    return validation


def benchmark_performance(signal: np.ndarray, fs: float, freqs: np.ndarray, 
                         ncyc: int, orders: Tuple[int, int], mult: bool = False,
                         num_runs: int = 3) -> Dict[str, Any]:
    """
    Benchmark performance of CPU vs GPU implementations.
    
    Args:
        signal: Input signal
        fs: Sampling frequency
        freqs: Frequency array
        ncyc: Number of cycles
        orders: Order range tuple
        mult: Multiplicative superresolution flag
        num_runs: Number of runs for averaging
    
    Returns:
        Dictionary with performance results
    """
    print(f"Benchmarking performance ({num_runs} runs)...")
    
    # Initialize memory monitor
    memory_monitor = GPUMemoryMonitor()
    
    # CPU benchmarking
    cpu_times = []
    print("  Running CPU benchmarks...")
    for run in range(num_runs):
        start_time = time.perf_counter()
        cpu_result = asltcx_cpu(signal, fs, freqs, ncyc, orders, mult)
        cpu_time = time.perf_counter() - start_time
        cpu_times.append(cpu_time)
        print(f"    Run {run+1}: {cpu_time:.4f}s")
    
    # GPU benchmarking
    gpu_times = []
    gpu_result = None
    if GPU_AVAILABLE:
        print("  Running GPU benchmarks...")
        memory_monitor.start_monitoring()
        
        for run in range(num_runs):
            memory_monitor.record_memory(f"Before GPU run {run+1}")
            
            start_time = time.perf_counter()
            gpu_result = asltcx_gpu(signal, fs, freqs, ncyc, orders, mult)
            gpu_time = time.perf_counter() - start_time
            gpu_times.append(gpu_time)
            
            memory_monitor.record_memory(f"After GPU run {run+1}")
            print(f"    Run {run+1}: {gpu_time:.4f}s")
        
        memory_monitor.print_summary()
    
    # Calculate statistics
    cpu_mean = np.mean(cpu_times)
    cpu_std = np.std(cpu_times)
    
    results = {
        'cpu_times': cpu_times,
        'cpu_mean': cpu_mean,
        'cpu_std': cpu_std,
        'cpu_result': cpu_result,
        'gpu_available': GPU_AVAILABLE
    }
    
    if GPU_AVAILABLE:
        gpu_mean = np.mean(gpu_times)
        gpu_std = np.std(gpu_times)
        speedup = cpu_mean / gpu_mean if gpu_mean > 0 else float('inf')
        
        results.update({
            'gpu_times': gpu_times,
            'gpu_mean': gpu_mean,
            'gpu_std': gpu_std,
            'gpu_result': gpu_result,
            'speedup': speedup,
            'peak_memory_mb': memory_monitor.get_peak_usage_mb()
        })
    
    return results


def create_comprehensive_comparison_plot(signal: np.ndarray, time_array: np.ndarray,
                                        fs: float, cpu_result: np.ndarray,
                                        gpu_result: Optional[np.ndarray],
                                        freqs: np.ndarray, trace_name: str,
                                        performance_results: Dict[str, Any]) -> plt.Figure:
    """
    Create comprehensive comparison plot showing CPU vs GPU results.

    Args:
        signal: Input signal
        time_array: Time array
        fs: Sampling frequency
        cpu_result: CPU computation result
        gpu_result: GPU computation result (None if GPU not available)
        freqs: Frequency array
        trace_name: Name of the trace
        performance_results: Performance benchmark results

    Returns:
        Matplotlib figure
    """
    # Determine number of columns based on GPU availability
    n_cols = 6 if gpu_result is not None else 4

    fig, axes = plt.subplots(1, n_cols, figsize=(4*n_cols, 10), sharey=True)
    fig.suptitle(f'ASLTCX GPU vs CPU Comprehensive Test - {trace_name}', fontsize=16)

    # Column 1: Original Signal
    axes[0].plot(signal, time_array, color='black', linewidth=0.8)
    axes[0].set_title('Original Signal')
    axes[0].set_xlabel('Amplitude')
    axes[0].set_ylabel('Time (s)')
    axes[0].grid(True, alpha=0.3)

    # CPU Results (Columns 2-4)
    cpu_magnitude = np.abs(cpu_result)
    cpu_voice = np.real(cpu_result)
    cpu_mag_voice = cpu_magnitude * cpu_voice

    # CPU Magnitude
    im1 = axes[1].pcolormesh(freqs, time_array, cpu_magnitude.T,
                            shading='auto', cmap='viridis')
    axes[1].set_title('CPU Magnitude')
    axes[1].set_xlabel('Frequency (Hz)')
    plt.colorbar(im1, ax=axes[1], shrink=0.8)

    # CPU Voice
    vmax_cpu = np.max(np.abs(cpu_voice))
    im2 = axes[2].pcolormesh(freqs, time_array, cpu_voice.T,
                            shading='auto', cmap='RdBu',
                            vmin=-vmax_cpu, vmax=vmax_cpu)
    axes[2].set_title('CPU Voice')
    axes[2].set_xlabel('Frequency (Hz)')
    plt.colorbar(im2, ax=axes[2], shrink=0.8)

    # CPU Magnitude * Voice
    im3 = axes[3].pcolormesh(freqs, time_array, cpu_mag_voice.T,
                            shading='auto', cmap='viridis')
    axes[3].set_title('CPU Mag×Voice')
    axes[3].set_xlabel('Frequency (Hz)')
    plt.colorbar(im3, ax=axes[3], shrink=0.8)

    if gpu_result is not None:
        # GPU Results (Columns 5-6)
        gpu_magnitude = np.abs(gpu_result)
        gpu_voice = np.real(gpu_result)

        # GPU Magnitude
        im4 = axes[4].pcolormesh(freqs, time_array, gpu_magnitude.T,
                                shading='auto', cmap='viridis')
        axes[4].set_title('GPU Magnitude')
        axes[4].set_xlabel('Frequency (Hz)')
        plt.colorbar(im4, ax=axes[4], shrink=0.8)

        # Difference plot (CPU - GPU)
        diff_magnitude = cpu_magnitude - gpu_magnitude
        max_diff = np.max(np.abs(diff_magnitude))
        im5 = axes[5].pcolormesh(freqs, time_array, diff_magnitude.T,
                                shading='auto', cmap='RdBu',
                                vmin=-max_diff, vmax=max_diff)
        axes[5].set_title(f'Difference (CPU-GPU)\nMax: {max_diff:.2e}')
        axes[5].set_xlabel('Frequency (Hz)')
        plt.colorbar(im5, ax=axes[5], shrink=0.8)

    # Set time limits for all axes
    for ax in axes:
        ax.set_ylim([time_array[0], time_array[-1]])
        if ax != axes[0]:
            ax.set_ylabel('')

    # Add performance information
    perf_text = f"CPU Time: {performance_results['cpu_mean']:.4f}±{performance_results['cpu_std']:.4f}s"
    if performance_results['gpu_available']:
        perf_text += f"\nGPU Time: {performance_results['gpu_mean']:.4f}±{performance_results['gpu_std']:.4f}s"
        perf_text += f"\nSpeedup: {performance_results['speedup']:.2f}x"
        perf_text += f"\nGPU Memory: {performance_results['peak_memory_mb']:.1f}MB"

    fig.text(0.5, 0.02, perf_text, ha='center', va='bottom',
             bbox=dict(boxstyle='round,pad=0.5', fc='lightblue', alpha=0.7))

    plt.tight_layout()
    plt.subplots_adjust(top=0.9, bottom=0.15)

    return fig


def run_comprehensive_test(test_name: str = "Comprehensive GPU Test") -> Dict[str, Any]:
    """
    Run comprehensive test of GPU vs CPU ASLTCX implementations.

    Args:
        test_name: Name of the test

    Returns:
        Dictionary with complete test results
    """
    print(f"\n{'='*60}")
    print(f"Running {test_name}")
    print(f"{'='*60}")

    # Generate test data
    print("Generating synthetic test data...")
    data, time_array, dt = generate_complex_synthetic_data(duration=2.0, fs=1000.0, num_traces=3)
    fs = 1.0 / dt

    print(f"  Data shape: {data.shape}")
    print(f"  Sampling rate: {fs:.1f} Hz")
    print(f"  Duration: {time_array[-1]:.2f} seconds")

    # Test parameters
    freqs = np.linspace(5, 100, 50)  # Frequency range
    ncyc = 3  # Number of cycles
    orders = (1, 8)  # Order range
    mult = False  # Additive superresolution

    print(f"  Frequency range: {freqs[0]:.1f} - {freqs[-1]:.1f} Hz ({len(freqs)} points)")
    print(f"  Cycles: {ncyc}, Orders: {orders}, Multiplicative: {mult}")

    # Test results storage
    test_results = {
        'test_name': test_name,
        'data_shape': data.shape,
        'fs': fs,
        'parameters': {
            'freqs': freqs,
            'ncyc': ncyc,
            'orders': orders,
            'mult': mult
        },
        'traces': {}
    }

    # Test each trace
    for i, trace_data in enumerate(data):
        trace_name = f"Trace_{i+1}"
        print(f"\n--- Testing {trace_name} ---")

        try:
            # Benchmark performance
            performance_results = benchmark_performance(
                trace_data, fs, freqs, ncyc, orders, mult, num_runs=3
            )

            # Validate GPU vs CPU equivalence if GPU is available
            validation_results = None
            if performance_results['gpu_available'] and performance_results['gpu_result'] is not None:
                validation_results = validate_gpu_cpu_equivalence(
                    performance_results['cpu_result'],
                    performance_results['gpu_result'],
                    tolerance=1e-10
                )

                print(f"\n  Validation Results:")
                for detail in validation_results['details']:
                    print(f"    {detail}")

            # Create comparison plot
            fig = create_comprehensive_comparison_plot(
                trace_data, time_array, fs,
                performance_results['cpu_result'],
                performance_results.get('gpu_result'),
                freqs, trace_name, performance_results
            )

            # Save plot
            filename = f'asltcx_gpu_test_{trace_name.lower()}.png'
            fig.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"  Plot saved as '{filename}'")

            # Store results
            test_results['traces'][trace_name] = {
                'performance': performance_results,
                'validation': validation_results,
                'plot_filename': filename
            }

            plt.close(fig)  # Free memory

        except Exception as e:
            print(f"  ✗ Error testing {trace_name}: {e}")
            test_results['traces'][trace_name] = {
                'error': str(e),
                'traceback': traceback.format_exc()
            }

    return test_results


def print_test_summary(test_results: Dict[str, Any]):
    """Print comprehensive test summary."""
    print(f"\n{'='*60}")
    print("COMPREHENSIVE TEST SUMMARY")
    print(f"{'='*60}")

    print(f"Test: {test_results['test_name']}")
    print(f"Data shape: {test_results['data_shape']}")
    print(f"Sampling rate: {test_results['fs']:.1f} Hz")

    # Performance summary
    total_cpu_time = 0
    total_gpu_time = 0
    successful_tests = 0
    failed_tests = 0
    validation_passed = 0
    validation_failed = 0

    print(f"\nPer-trace results:")
    for trace_name, results in test_results['traces'].items():
        if 'error' in results:
            print(f"  {trace_name}: ✗ FAILED - {results['error']}")
            failed_tests += 1
        else:
            successful_tests += 1
            perf = results['performance']
            total_cpu_time += perf['cpu_mean']

            print(f"  {trace_name}:")
            print(f"    CPU: {perf['cpu_mean']:.4f}±{perf['cpu_std']:.4f}s")

            if perf['gpu_available']:
                total_gpu_time += perf['gpu_mean']
                print(f"    GPU: {perf['gpu_mean']:.4f}±{perf['gpu_std']:.4f}s")
                print(f"    Speedup: {perf['speedup']:.2f}x")
                print(f"    GPU Memory: {perf['peak_memory_mb']:.1f}MB")

                if results['validation']:
                    if results['validation']['passed']:
                        print(f"    Validation: ✓ PASSED")
                        validation_passed += 1
                    else:
                        print(f"    Validation: ✗ FAILED")
                        validation_failed += 1
            else:
                print(f"    GPU: Not available")

    print(f"\nOverall Summary:")
    print(f"  Successful tests: {successful_tests}")
    print(f"  Failed tests: {failed_tests}")
    print(f"  Total CPU time: {total_cpu_time:.4f}s")

    if total_gpu_time > 0:
        overall_speedup = total_cpu_time / total_gpu_time
        print(f"  Total GPU time: {total_gpu_time:.4f}s")
        print(f"  Overall speedup: {overall_speedup:.2f}x")
        print(f"  Validation passed: {validation_passed}")
        print(f"  Validation failed: {validation_failed}")

    print(f"{'='*60}")


def main():
    """Main function to run comprehensive GPU tests."""
    try:
        # Run comprehensive test
        test_results = run_comprehensive_test("ASLTCX GPU Comprehensive Test")

        # Print summary
        print_test_summary(test_results)

        # Show plots if requested
        import matplotlib
        if matplotlib.get_backend() != 'Agg':
            show_plots = input("\nShow plots? (y/n): ").lower().strip() == 'y'
            if show_plots:
                plt.show()

        print(f"\nTest completed successfully!")
        print(f"Generated {len(test_results['traces'])} test plots.")

    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"\nFatal error during testing: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
