#!/usr/bin/env python3
"""
Complete ASLTCX GPU Test Suite Runner

This script runs the complete GPU-accelerated test suite for ASLTCX implementation,
integrating all testing components: validation, benchmarking, error handling, and
comprehensive data generation.

Usage:
    python run_asltcx_gpu_test_suite.py [--quick] [--no-plots] [--save-results]
"""

import sys
import os
import argparse
import time
import json
from typing import Dict, Any, List
import warnings

# Add the demo_script directory to path for imports
demo_dir = os.path.dirname(os.path.abspath(__file__))
if demo_dir not in sys.path:
    sys.path.insert(0, demo_dir)

# Import all test modules
try:
    from test_asltcx_gpu_comprehensive import run_comprehensive_test, print_test_summary
    from asltcx_gpu_validation_utils import ValidationResult, advanced_numerical_comparison, print_validation_summary
    from asltcx_gpu_benchmark import benchmark_scalability, benchmark_frequency_scaling, print_benchmark_summary
    from asltcx_gpu_error_handling import check_gpu_health, print_gpu_health_report, GPU_AVAILABLE
    from asltcx_test_data_generator import generate_standard_test_data, SeismicDataGenerator
    print("✓ All test modules imported successfully")
except ImportError as e:
    print(f"✗ Failed to import test modules: {e}")
    print("Make sure all test files are in the same directory")
    sys.exit(1)

# Import ASLTCX implementations
code_dir = os.path.join(demo_dir, '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

try:
    from asltcx import asltcx as asltcx_cpu
    print("✓ CPU ASLTCX imported")
except ImportError as e:
    print(f"✗ Failed to import CPU ASLTCX: {e}")
    sys.exit(1)

if GPU_AVAILABLE:
    try:
        from asltcx_gpu import asltcx as asltcx_gpu
        print("✓ GPU ASLTCX imported")
    except ImportError as e:
        print(f"✗ Failed to import GPU ASLTCX: {e}")
        print("GPU tests will be skipped")


class TestSuiteRunner:
    """Complete test suite runner for ASLTCX GPU implementation."""
    
    def __init__(self, quick_mode: bool = False, save_results: bool = False):
        self.quick_mode = quick_mode
        self.save_results = save_results
        self.results = {
            'timestamp': time.time(),
            'gpu_available': GPU_AVAILABLE,
            'quick_mode': quick_mode,
            'tests': {}
        }
    
    def run_health_check(self) -> bool:
        """Run GPU health check."""
        print("\n" + "="*60)
        print("STEP 1: GPU HEALTH CHECK")
        print("="*60)
        
        print_gpu_health_report()
        
        health_status = check_gpu_health()
        self.results['tests']['health_check'] = health_status
        
        if not GPU_AVAILABLE:
            print("\n⚠️  GPU not available - only CPU tests will be run")
            return False
        
        if health_status['overall_status'] != 'healthy':
            print(f"\n⚠️  GPU health status: {health_status['overall_status']}")
            print("Proceeding with tests but results may be affected")
        
        return True
    
    def run_validation_tests(self) -> Dict[str, Any]:
        """Run validation tests comparing GPU vs CPU."""
        print("\n" + "="*60)
        print("STEP 2: GPU vs CPU VALIDATION TESTS")
        print("="*60)
        
        if not GPU_AVAILABLE:
            print("Skipping validation tests - GPU not available")
            return {'skipped': True, 'reason': 'GPU not available'}
        
        # Generate test data
        print("Generating validation test data...")
        test_data = generate_standard_test_data(
            duration=1.0 if self.quick_mode else 2.0,
            fs=1000.0,
            n_traces=2 if self.quick_mode else 3,
            random_seed=42
        )
        
        # Test parameters
        freqs = np.linspace(10, 100, 20 if self.quick_mode else 40)
        ncyc = 3
        orders = (1, 4 if self.quick_mode else 6)
        
        validation_results = []
        
        for i, trace_data in enumerate(test_data['data']):
            print(f"\nValidating trace {i+1}/{len(test_data['data'])}...")
            
            try:
                # Run CPU implementation
                cpu_result = asltcx_cpu(trace_data, test_data['fs'], freqs, ncyc, orders, False)
                
                # Run GPU implementation
                gpu_result = asltcx_gpu(trace_data, test_data['fs'], freqs, ncyc, orders, False)
                
                # Validate results
                validation = advanced_numerical_comparison(cpu_result, gpu_result, tolerance=1e-10)
                validation_results.append(validation)
                
                print(f"  Validation: {'PASSED' if validation.passed else 'FAILED'}")
                print(f"  Max error: {validation.max_abs_error:.2e}")
                print(f"  Correlation: {validation.correlation:.6f}")
                
            except Exception as e:
                print(f"  ✗ Validation failed: {e}")
                validation_results.append(None)
        
        # Print summary
        valid_results = [r for r in validation_results if r is not None]
        if valid_results:
            print_validation_summary(valid_results)
        
        return {
            'validation_results': validation_results,
            'test_parameters': {
                'freqs': freqs.tolist(),
                'ncyc': ncyc,
                'orders': orders,
                'data_shape': test_data['data'].shape
            }
        }
    
    def run_performance_benchmarks(self) -> Dict[str, Any]:
        """Run performance benchmarking tests."""
        print("\n" + "="*60)
        print("STEP 3: PERFORMANCE BENCHMARKS")
        print("="*60)
        
        if not GPU_AVAILABLE:
            print("Skipping performance benchmarks - GPU not available")
            return {'skipped': True, 'reason': 'GPU not available'}
        
        # Generate benchmark data
        print("Generating benchmark test data...")
        generator = SeismicDataGenerator(fs=1000.0, random_seed=123)
        _, base_signal = generator.generate_complex_trace(1.0, {
            'wavelets': [{'freq': 30, 'type': 'ricker', 'amplitude': 1.0}],
            'reflectors': {'count': 8, 'amplitude_range': (-0.5, 0.5)},
            'noise': {'snr_db': 20, 'type': 'white'},
            'attenuation': {'q_factor': 50, 'reference_freq': 30}
        })
        
        freqs = np.linspace(10, 80, 30)
        ncyc = 3
        orders = (1, 5)
        
        # Scalability benchmark
        print("\nRunning scalability benchmark...")
        scale_factors = [0.5, 1.0, 2.0] if self.quick_mode else [0.5, 1.0, 2.0, 4.0]
        scalability_results = benchmark_scalability(
            asltcx_cpu, asltcx_gpu, base_signal, 1000.0, freqs, ncyc, orders, scale_factors
        )
        
        # Frequency scaling benchmark
        print("\nRunning frequency scaling benchmark...")
        freq_scales = [0.5, 1.0, 2.0] if self.quick_mode else [0.5, 1.0, 2.0, 4.0]
        frequency_results = benchmark_frequency_scaling(
            asltcx_cpu, asltcx_gpu, base_signal, 1000.0, freqs, ncyc, orders, freq_scales
        )
        
        # Print summary
        print_benchmark_summary(scalability_results, frequency_results)
        
        return {
            'scalability_results': scalability_results,
            'frequency_results': frequency_results
        }
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run the comprehensive integration test."""
        print("\n" + "="*60)
        print("STEP 4: COMPREHENSIVE INTEGRATION TEST")
        print("="*60)
        
        # Run the comprehensive test
        test_name = "Quick GPU Test" if self.quick_mode else "Full GPU Test Suite"
        comprehensive_results = run_comprehensive_test(test_name)
        
        # Print summary
        print_test_summary(comprehensive_results)
        
        return comprehensive_results
    
    def save_test_results(self):
        """Save test results to JSON file."""
        if not self.save_results:
            return
        
        timestamp_str = time.strftime("%Y%m%d_%H%M%S", time.localtime(self.results['timestamp']))
        filename = f"asltcx_gpu_test_results_{timestamp_str}.json"
        
        # Convert numpy arrays to lists for JSON serialization
        def convert_for_json(obj):
            if hasattr(obj, 'tolist'):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_for_json(item) for item in obj]
            else:
                return obj
        
        try:
            json_results = convert_for_json(self.results)
            with open(filename, 'w') as f:
                json.dump(json_results, f, indent=2, default=str)
            print(f"\n📁 Test results saved to: {filename}")
        except Exception as e:
            print(f"\n⚠️  Failed to save results: {e}")
    
    def run_complete_suite(self):
        """Run the complete test suite."""
        print("🚀 Starting ASLTCX GPU Test Suite")
        print(f"Mode: {'Quick' if self.quick_mode else 'Full'}")
        print(f"Save results: {'Yes' if self.save_results else 'No'}")
        
        start_time = time.time()
        
        try:
            # Step 1: Health check
            gpu_healthy = self.run_health_check()
            
            # Step 2: Validation tests
            self.results['tests']['validation'] = self.run_validation_tests()
            
            # Step 3: Performance benchmarks
            self.results['tests']['performance'] = self.run_performance_benchmarks()
            
            # Step 4: Comprehensive test
            self.results['tests']['comprehensive'] = self.run_comprehensive_test()
            
            # Calculate total time
            total_time = time.time() - start_time
            self.results['total_time_seconds'] = total_time
            
            # Print final summary
            print("\n" + "="*60)
            print("🎉 TEST SUITE COMPLETE")
            print("="*60)
            print(f"Total execution time: {total_time:.2f} seconds")
            print(f"GPU available: {GPU_AVAILABLE}")
            
            if GPU_AVAILABLE:
                validation_tests = self.results['tests'].get('validation', {})
                if 'validation_results' in validation_tests:
                    valid_results = [r for r in validation_tests['validation_results'] if r is not None and r.passed]
                    total_validations = len([r for r in validation_tests['validation_results'] if r is not None])
                    print(f"Validation tests passed: {len(valid_results)}/{total_validations}")
            
            print("="*60)
            
            # Save results
            self.save_test_results()
            
        except KeyboardInterrupt:
            print("\n\n⚠️  Test suite interrupted by user")
            self.results['interrupted'] = True
            self.save_test_results()
        except Exception as e:
            print(f"\n\n❌ Test suite failed with error: {e}")
            import traceback
            traceback.print_exc()
            self.results['error'] = str(e)
            self.save_test_results()
            sys.exit(1)


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description='Run ASLTCX GPU Test Suite')
    parser.add_argument('--quick', action='store_true', 
                       help='Run quick test mode (reduced data sizes and iterations)')
    parser.add_argument('--no-plots', action='store_true',
                       help='Skip plot generation and display')
    parser.add_argument('--save-results', action='store_true',
                       help='Save test results to JSON file')
    
    args = parser.parse_args()
    
    # Set matplotlib backend if no plots requested
    if args.no_plots:
        import matplotlib
        matplotlib.use('Agg')
    
    # Create and run test suite
    runner = TestSuiteRunner(quick_mode=args.quick, save_results=args.save_results)
    runner.run_complete_suite()


if __name__ == "__main__":
    main()
