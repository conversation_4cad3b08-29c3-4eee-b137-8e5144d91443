import cupy as cp
from cupyx.scipy.signal import fftconvolve
from typing import Any

# Dtype aliases to appease static type checkers
CP_FLOAT64: Any = cp.float64  # type: ignore[assignment]
CP_COMPLEX128: Any = cp.complex128  # type: ignore[assignment]


def gauss(t, sd):
    """
    Computes the Gaussian coefficient (CuPy vectorized).
    """
    cnorm = 1.0 / (sd * cp.sqrt(2.0 * cp.pi))
    return cnorm * cp.exp(-(t**2) / (2.0 * sd**2))


def bw_cf(t, bw, cf):
    """
    Computes complex wavelet coefficients (CuPy vectorized).
    """
    cnorm = 1.0 / (bw * cp.sqrt(2.0 * cp.pi))
    exp1 = cnorm * cp.exp(-(t**2) / (2.0 * bw**2))
    return cp.exp(2j * cp.pi * cf * t) * exp1


def morlet(Fc, Nc, Fs):
    """
    Complex Morlet wavelet generator on GPU (CuPy).
    """
    if abs(Fc) < 1e-10:
        Fc = 0.001 if Fc >= 0 else -0.001

    Fc_cp = cp.asarray(Fc, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    Nc_cp = cp.asarray(Nc, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    Fs_cp = cp.asarray(Fs, dtype=CP_FLOAT64)  # type: ignore[arg-type]

    # The last peak should be at 2.5 standard deviations
    sd = (Nc_cp / 2.0) * (1.0 / cp.abs(Fc_cp)) / 2.5

    # window length ~ 6 * sd * Fs, force odd
    wl_float = 6.0 * sd * Fs_cp
    wl_even = int(2 * cp.floor(cp.fix(wl_float).astype(CP_FLOAT64) / 2.0).item())  # type: ignore[arg-type]
    wl = wl_even + 1

    off = wl // 2
    i = cp.arange(wl, dtype=CP_FLOAT64)  # type: ignore[arg-type]
    t = (i - off) / Fs_cp

    w = bw_cf(t, sd, Fc_cp)
    gi = gauss(t, sd).sum()
    w = w / gi
    return w.astype(CP_COMPLEX128, copy=False)  # type: ignore[arg-type]


def fractional(x: float) -> int:
    """
    Return 1 if x has a fractional part, otherwise 0.
    """
    try:
        xf = float(x)
    except Exception:
        xf = float(cp.asnumpy(x))
    return 1 if float(int(xf)) != xf else 0


class SuperletTransformCX_GPU:
    """
    GPU version of the complex Superlet Transform.
    Mirrors the API and logic of code/superletcx.py::SuperletTransformCX
    but performs computations on CuPy arrays.
    """

    def __init__(
        self,
        inputSize,
        samplingRate,
        frequencyRange,
        frequencyBins,
        baseCycles,
        superletOrders,
        frequencies=None,
    ):
        """
        Initialize the superlet transform.
        Arguments:
            inputSize: size of the input in samples
            samplingRate: the sampling rate of the input signal in Hz
            frequencyRange: tuple of ascending frequency points, in Hz
            frequencyBins: number of frequency bins to sample in the interval frequencyRange
            baseCycles: number of cycles of the smallest wavelet (c1 in the paper)
            superletOrders: a tuple containing the range of superlet orders, linearly distributed along frequencyRange
            frequencies: specific list of frequencies - can be provided instead of frequencyRange
        """
        self.clear()

        # initialize frequency vector
        if frequencies is not None:
            frequencyBins = len(frequencies)
            # store as host list for compatibility, but keep GPU copy too
            self.frequencies = cp.asarray(frequencies, dtype=CP_FLOAT64)  # type: ignore[arg-type]
        else:
            freq_host = __import__("numpy").linspace(
                start=frequencyRange[0], stop=frequencyRange[1], num=frequencyBins
            )
            self.frequencies = cp.asarray(freq_host, dtype=CP_FLOAT64)  # type: ignore[arg-type]

        self.inputSize = int(inputSize)
        self.orders = cp.asarray(
            __import__("numpy").linspace(
                start=superletOrders[0], stop=superletOrders[1], num=frequencyBins
            ),
            dtype=CP_FLOAT64,  # type: ignore[arg-type]
        )

        # working buffers on GPU
        self.convBuffer = cp.zeros(self.inputSize, dtype=CP_COMPLEX128)  # type: ignore[arg-type]
        self.phaseBuffer = cp.zeros(self.inputSize, dtype=CP_COMPLEX128)  # type: ignore[arg-type]
        self.absBuffer = cp.zeros(self.inputSize, dtype=CP_FLOAT64)  # type: ignore[arg-type]
        self.magnitudeBuffer = cp.zeros(self.inputSize, dtype=CP_FLOAT64)  # type: ignore[arg-type]
        self.superlets = []

        # pre-create wavelets
        for iFreq in range(frequencyBins):
            centerFreq = float(self.frequencies[iFreq].item())
            nWavelets = int(cp.ceil(self.orders[iFreq]).item())
            self.superlets.append([])
            for iWave in range(nWavelets):
                self.superlets[iFreq].append(
                    morlet(centerFreq, (iWave + 1) * baseCycles, samplingRate)
                )

    def clear(self):
        """
        Clear the transform allocations.
        Keep attributes in valid, sized states to satisfy static type checkers.
        """
        # Reset wavelets and metadata
        self.superlets = []
        self.frequencies = cp.asarray([], dtype=CP_FLOAT64)  # type: ignore[arg-type]
        self.orders = cp.asarray([], dtype=CP_FLOAT64)  # type: ignore[arg-type]
        # Reset working buffers to zero (preserve shapes to keep methods available)
        self.magnitudeBuffer.fill(0)
        self.phaseBuffer.fill(0)
        self.absBuffer.fill(0)
        self.convBuffer.fill(0)

    def transform(self, inputData):
        """
        Apply the superlet transform on a single data buffer (1D array).
        Arguments:
            inputData: A 1xInputSize array containing the signal to be transformed (host or CuPy array).
        """
        # Ensure 1D of correct length
        if len(inputData.shape) != 1 or inputData.shape[0] != self.inputSize:
            raise Exception("Invalid input size.")

        # Move input to GPU if needed
        input_gpu = cp.asarray(inputData, dtype=CP_COMPLEX128)  # type: ignore[arg-type]

        nFreqs = int(self.frequencies.shape[0])
        result = cp.zeros((nFreqs, self.inputSize), dtype=CP_COMPLEX128)  # type: ignore[arg-type]

        for iFreq in range(nFreqs):
            # init pooling buffers
            self.magnitudeBuffer.fill(1)
            self.phaseBuffer.fill(0)

            if len(self.superlets[iFreq]) > 1:
                # superlet
                nWavelets = int(cp.floor(self.orders[iFreq]).item())
                rfactor = 1.0 / nWavelets

                for iWave in range(nWavelets):
                    self.convBuffer = fftconvolve(input_gpu, self.superlets[iFreq][iWave], "same")
                    self.absBuffer = cp.abs(self.convBuffer)
                    self.magnitudeBuffer *= self.absBuffer
                    self.phaseBuffer += self.convBuffer / (self.absBuffer + 1e-12)

                if fractional(float(self.orders[iFreq])) != 0 and len(self.superlets[iFreq]) == nWavelets + 1:
                    # apply the fractional wavelet
                    exponent = float(self.orders[iFreq]) - nWavelets
                    rfactor = 1.0 / (nWavelets + exponent)

                    self.convBuffer = fftconvolve(input_gpu, self.superlets[iFreq][nWavelets], "same")
                    self.absBuffer = cp.abs(self.convBuffer)
                    # Prevent underflow
                    self.magnitudeBuffer *= cp.maximum(self.absBuffer, 1e-300) ** exponent
                    self.phaseBuffer += (self.convBuffer / (self.absBuffer + 1e-12)) * exponent

                # perform geometric mean and combine with phase
                result[iFreq, :] += (self.magnitudeBuffer ** rfactor) * (self.phaseBuffer / float(self.orders[iFreq]))

            else:
                # single wavelet transform
                result[iFreq, :] += fftconvolve(input_gpu, self.superlets[iFreq][0], "same")

        return cp.asnumpy(result)


def superlets(data, fs, foi, c1, ord):
    """
    Perform fractional adaptive superlet transform (FASLT) on a single trial (complex output) on GPU.

    Arguments:
        data: a numpy array of data (1D)
        fs: the sampling rate in Hz
        foi: list of frequencies of interest
        c1: base number of cycles parameter
        ord: the order (for SLT) or order range (for FASLT), spanned across the frequencies of interest

    Returns:
        A matrix containing the complex superlet spectrum (host ndarray).
    """
    # determine buffer size
    bufferSize = data.shape[len(data.shape) - 1]

    # normalize order parameter
    if len(ord) == 1:
        ord = (ord, ord)

    # build analyzer
    faslt = SuperletTransformCX_GPU(
        inputSize=bufferSize,
        frequencyRange=None,
        frequencyBins=None,
        samplingRate=fs,
        frequencies=foi,
        baseCycles=c1,
        superletOrders=ord,
    )

    # apply transform
    result = faslt.transform(data)
    faslt.clear()
    return result