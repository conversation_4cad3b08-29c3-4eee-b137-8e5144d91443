#!/usr/bin/env python3
"""
GPU Error Handling and Memory Management for ASLTCX Implementation

This module provides comprehensive error handling, memory management, and recovery
mechanisms for GPU-accelerated ASLTCX computations.
"""

import numpy as np
import sys
import os
import warnings
import functools
from typing import Dict, List, Tuple, Optional, Any, Callable, Union
import traceback
import time

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

# GPU availability and imports
GPU_AVAILABLE = False
GPU_ERROR_MSG = ""
try:
    import cupy as cp
    device_count = cp.cuda.runtime.getDeviceCount()
    if device_count > 0:
        # Test basic GPU operation
        test_array = cp.array([1, 2, 3])
        _ = cp.sum(test_array)
        GPU_AVAILABLE = True
    else:
        GPU_ERROR_MSG = "No CUDA devices found"
except ImportError as e:
    GPU_ERROR_MSG = f"CuPy not available: {e}"
except Exception as e:
    GPU_ERROR_MSG = f"GPU initialization failed: {e}"


class GPUMemoryManager:
    """Comprehensive GPU memory management with automatic cleanup."""
    
    def __init__(self, memory_limit_gb: Optional[float] = None):
        self.memory_limit_gb = memory_limit_gb
        self.allocated_arrays = []
        self.memory_pool = None
        self.initial_memory = None
        
        if GPU_AVAILABLE:
            self.memory_pool = cp.get_default_memory_pool()
            self.initial_memory = cp.cuda.runtime.memGetInfo()
    
    def __enter__(self):
        """Context manager entry."""
        if GPU_AVAILABLE and self.memory_pool:
            self.memory_pool.free_all_blocks()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
    
    def allocate_array(self, shape: Tuple[int, ...], dtype: np.dtype) -> Optional[cp.ndarray]:
        """
        Safely allocate GPU array with memory checking.
        
        Args:
            shape: Array shape
            dtype: Array data type
        
        Returns:
            CuPy array or None if allocation fails
        """
        if not GPU_AVAILABLE:
            return None
        
        try:
            # Estimate memory requirement
            element_size = np.dtype(dtype).itemsize
            required_bytes = np.prod(shape) * element_size
            required_gb = required_bytes / (1024**3)
            
            # Check memory limit
            if self.memory_limit_gb and required_gb > self.memory_limit_gb:
                raise MemoryError(f"Requested allocation ({required_gb:.2f}GB) exceeds limit ({self.memory_limit_gb:.2f}GB)")
            
            # Check available memory
            free_memory, total_memory = cp.cuda.runtime.memGetInfo()
            if required_bytes > free_memory * 0.9:  # Leave 10% buffer
                raise MemoryError(f"Insufficient GPU memory: need {required_gb:.2f}GB, have {free_memory/(1024**3):.2f}GB")
            
            # Allocate array
            array = cp.zeros(shape, dtype=dtype)
            self.allocated_arrays.append(array)
            
            return array
            
        except Exception as e:
            warnings.warn(f"GPU memory allocation failed: {e}")
            return None
    
    def get_memory_info(self) -> Dict[str, float]:
        """Get current GPU memory information."""
        if not GPU_AVAILABLE:
            return {'error': 'GPU not available'}
        
        try:
            free_memory, total_memory = cp.cuda.runtime.memGetInfo()
            used_memory = total_memory - free_memory
            
            return {
                'free_gb': free_memory / (1024**3),
                'used_gb': used_memory / (1024**3),
                'total_gb': total_memory / (1024**3),
                'utilization_percent': (used_memory / total_memory) * 100,
                'allocated_arrays': len(self.allocated_arrays)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def cleanup(self):
        """Clean up allocated GPU memory."""
        if GPU_AVAILABLE:
            # Clear tracked arrays
            self.allocated_arrays.clear()
            
            # Free memory pool
            if self.memory_pool:
                self.memory_pool.free_all_blocks()
    
    def print_memory_status(self):
        """Print current memory status."""
        info = self.get_memory_info()
        if 'error' in info:
            print(f"Memory status error: {info['error']}")
        else:
            print(f"GPU Memory: {info['used_gb']:.2f}GB used / {info['total_gb']:.2f}GB total "
                  f"({info['utilization_percent']:.1f}% utilization)")


class GPUErrorHandler:
    """Comprehensive GPU error handling with fallback mechanisms."""
    
    def __init__(self, fallback_to_cpu: bool = True, max_retries: int = 3):
        self.fallback_to_cpu = fallback_to_cpu
        self.max_retries = max_retries
        self.error_history = []
    
    def handle_gpu_error(self, error: Exception, context: str = "") -> Dict[str, Any]:
        """
        Handle GPU errors with detailed analysis.
        
        Args:
            error: The exception that occurred
            context: Context information about where the error occurred
        
        Returns:
            Dictionary with error analysis and recommendations
        """
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': time.time(),
            'recoverable': False,
            'recommendations': []
        }
        
        # Analyze specific error types
        if isinstance(error, cp.cuda.memory.OutOfMemoryError):
            error_info['category'] = 'memory'
            error_info['recoverable'] = True
            error_info['recommendations'].extend([
                'Reduce input data size',
                'Use smaller frequency arrays',
                'Enable memory pooling',
                'Clear GPU memory cache'
            ])
            
        elif isinstance(error, cp.cuda.runtime.CUDARuntimeError):
            error_info['category'] = 'cuda_runtime'
            error_code = getattr(error, 'status', None)
            if error_code == 2:  # CUDA_ERROR_OUT_OF_MEMORY
                error_info['recoverable'] = True
                error_info['recommendations'].append('GPU out of memory - reduce data size')
            elif error_code == 35:  # CUDA_ERROR_CONTEXT_IS_DESTROYED
                error_info['recommendations'].append('CUDA context destroyed - restart required')
            else:
                error_info['recommendations'].append('CUDA runtime error - check GPU status')
                
        elif isinstance(error, ImportError):
            error_info['category'] = 'import'
            error_info['recommendations'].extend([
                'Install CuPy: pip install cupy-cuda11x',
                'Check CUDA installation',
                'Verify GPU drivers'
            ])
            
        elif isinstance(error, AttributeError):
            error_info['category'] = 'api'
            error_info['recoverable'] = True
            error_info['recommendations'].append('Check CuPy version compatibility')
            
        else:
            error_info['category'] = 'unknown'
            error_info['recommendations'].append('Check GPU availability and CUDA installation')
        
        # Store error history
        self.error_history.append(error_info)
        
        return error_info
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors encountered."""
        if not self.error_history:
            return {'total_errors': 0}
        
        error_types = {}
        error_categories = {}
        
        for error in self.error_history:
            error_type = error['error_type']
            error_category = error.get('category', 'unknown')
            
            error_types[error_type] = error_types.get(error_type, 0) + 1
            error_categories[error_category] = error_categories.get(error_category, 0) + 1
        
        return {
            'total_errors': len(self.error_history),
            'error_types': error_types,
            'error_categories': error_categories,
            'most_recent': self.error_history[-1] if self.error_history else None
        }


def gpu_safe_wrapper(fallback_func: Optional[Callable] = None, 
                    max_retries: int = 3,
                    memory_limit_gb: Optional[float] = None):
    """
    Decorator for GPU-safe function execution with automatic fallback.
    
    Args:
        fallback_func: CPU fallback function
        max_retries: Maximum number of retry attempts
        memory_limit_gb: GPU memory limit in GB
    
    Returns:
        Decorated function with error handling
    """
    def decorator(gpu_func: Callable):
        @functools.wraps(gpu_func)
        def wrapper(*args, **kwargs):
            error_handler = GPUErrorHandler(fallback_to_cpu=fallback_func is not None, 
                                          max_retries=max_retries)
            
            # Check GPU availability first
            if not GPU_AVAILABLE:
                if fallback_func:
                    warnings.warn(f"GPU not available ({GPU_ERROR_MSG}), falling back to CPU")
                    return fallback_func(*args, **kwargs)
                else:
                    raise RuntimeError(f"GPU required but not available: {GPU_ERROR_MSG}")
            
            # Try GPU execution with retries
            for attempt in range(max_retries):
                try:
                    with GPUMemoryManager(memory_limit_gb) as memory_manager:
                        result = gpu_func(*args, **kwargs)
                        return result
                        
                except Exception as e:
                    error_info = error_handler.handle_gpu_error(e, f"Attempt {attempt+1}/{max_retries}")
                    
                    if attempt < max_retries - 1 and error_info['recoverable']:
                        warnings.warn(f"GPU error on attempt {attempt+1}, retrying: {e}")
                        # Clear GPU memory before retry
                        if GPU_AVAILABLE:
                            cp.get_default_memory_pool().free_all_blocks()
                        time.sleep(0.1)  # Brief pause before retry
                        continue
                    else:
                        # Final attempt failed
                        if fallback_func and error_info['recoverable']:
                            warnings.warn(f"GPU execution failed after {max_retries} attempts, falling back to CPU: {e}")
                            return fallback_func(*args, **kwargs)
                        else:
                            # Re-raise with additional context
                            raise RuntimeError(f"GPU execution failed: {e}. Recommendations: {', '.join(error_info['recommendations'])}") from e
            
            # Should not reach here
            raise RuntimeError("Unexpected error in GPU execution wrapper")
        
        return wrapper
    return decorator


def check_gpu_health() -> Dict[str, Any]:
    """
    Comprehensive GPU health check.
    
    Returns:
        Dictionary with GPU health status
    """
    health_status = {
        'gpu_available': GPU_AVAILABLE,
        'timestamp': time.time(),
        'checks': {}
    }
    
    if not GPU_AVAILABLE:
        health_status['error'] = GPU_ERROR_MSG
        return health_status
    
    try:
        # Basic CUDA runtime check
        device_count = cp.cuda.runtime.getDeviceCount()
        health_status['checks']['device_count'] = {
            'status': 'pass' if device_count > 0 else 'fail',
            'value': device_count
        }
        
        # Memory check
        free_memory, total_memory = cp.cuda.runtime.memGetInfo()
        memory_utilization = (total_memory - free_memory) / total_memory * 100
        health_status['checks']['memory'] = {
            'status': 'pass' if memory_utilization < 90 else 'warning',
            'free_gb': free_memory / (1024**3),
            'total_gb': total_memory / (1024**3),
            'utilization_percent': memory_utilization
        }
        
        # Basic computation check
        try:
            test_array = cp.random.randn(1000, 1000)
            result = cp.sum(test_array)
            _ = float(result)  # Transfer to CPU
            health_status['checks']['computation'] = {'status': 'pass'}
        except Exception as e:
            health_status['checks']['computation'] = {
                'status': 'fail',
                'error': str(e)
            }
        
        # Device properties
        device = cp.cuda.Device()
        props = device.attributes
        health_status['device_info'] = {
            'name': device.name,
            'compute_capability': f"{props['ComputeCapabilityMajor']}.{props['ComputeCapabilityMinor']}",
            'multiprocessor_count': props['MultiProcessorCount'],
            'max_threads_per_block': props['MaxThreadsPerBlock']
        }
        
    except Exception as e:
        health_status['checks']['general'] = {
            'status': 'fail',
            'error': str(e)
        }
    
    # Overall status
    all_checks = [check.get('status', 'fail') for check in health_status['checks'].values()]
    if all(status == 'pass' for status in all_checks):
        health_status['overall_status'] = 'healthy'
    elif any(status == 'fail' for status in all_checks):
        health_status['overall_status'] = 'unhealthy'
    else:
        health_status['overall_status'] = 'warning'
    
    return health_status


def print_gpu_health_report():
    """Print comprehensive GPU health report."""
    print("\n" + "="*50)
    print("GPU HEALTH REPORT")
    print("="*50)
    
    health = check_gpu_health()
    
    print(f"Overall Status: {health['overall_status'].upper()}")
    print(f"GPU Available: {health['gpu_available']}")
    
    if not health['gpu_available']:
        print(f"Error: {health.get('error', 'Unknown error')}")
        print("="*50)
        return
    
    if 'device_info' in health:
        info = health['device_info']
        print(f"\nDevice Information:")
        print(f"  Name: {info['name']}")
        print(f"  Compute Capability: {info['compute_capability']}")
        print(f"  Multiprocessors: {info['multiprocessor_count']}")
        print(f"  Max Threads/Block: {info['max_threads_per_block']}")
    
    print(f"\nHealth Checks:")
    for check_name, check_result in health['checks'].items():
        status = check_result['status'].upper()
        print(f"  {check_name.title()}: {status}")
        
        if check_name == 'memory':
            print(f"    Free: {check_result['free_gb']:.2f}GB")
            print(f"    Total: {check_result['total_gb']:.2f}GB")
            print(f"    Utilization: {check_result['utilization_percent']:.1f}%")
        
        if 'error' in check_result:
            print(f"    Error: {check_result['error']}")
    
    print("="*50)


if __name__ == "__main__":
    # Run GPU health check when module is executed directly
    print_gpu_health_report()
